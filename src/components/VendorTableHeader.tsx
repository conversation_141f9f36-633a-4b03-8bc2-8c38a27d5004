import * as React from "react";
import { ArrowUpDown, ArrowUp, ArrowDown } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { SearchInput } from "@/components/SearchInput";

export interface SortConfig {
  field: string;
  direction: "asc" | "desc" | null;
}

interface VendorTableHeaderProps {
  searchTerm: string;
  onSearchChange: (search: string) => void;

  // Sort props
  sortConfig?: SortConfig;
  onSortChange?: (sort: SortConfig) => void;
}

export function VendorTableHeader({ searchTerm, onSearchChange, sortConfig, onSortChange }: VendorTableHeaderProps) {
  const handleSort = (field: string) => {
    if (!onSortChange) return;

    let direction: "asc" | "desc" | null = "asc";

    if (sortConfig?.field === field) {
      if (sortConfig.direction === "asc") {
        direction = "desc";
      } else if (sortConfig.direction === "desc") {
        direction = null;
      }
    }

    onSortChange({ field, direction });
  };

  const getSortIcon = (field: string) => {
    if (sortConfig?.field !== field || !sortConfig.direction) {
      return <ArrowUpDown className="ml-2 h-4 w-4" />;
    }

    return sortConfig.direction === "asc" ? <ArrowUp className="ml-2 h-4 w-4" /> : <ArrowDown className="ml-2 h-4 w-4" />;
  };

  return (
    <>
      {/* Filter Row */}
      <TableRow className="border-b-2">
        <TableHead className="p-2">
          <SearchInput value={searchTerm} onChange={onSearchChange} placeholder="Search vendors..." className="w-full" />
        </TableHead>
        <TableHead className="p-2">{/* Location column - no filter for now */}</TableHead>
        <TableHead className="p-2">{/* Contact column - no filter for now */}</TableHead>
        <TableHead className="p-2">{/* VAT Number column - no filter for now */}</TableHead>
        <TableHead className="p-2">{/* Invoices column - no filter for now */}</TableHead>
        <TableHead className="p-2">{/* Total Amount column - no filter for now */}</TableHead>
        <TableHead className="p-2">{/* Invoice Kind column - no filter for now */}</TableHead>
        <TableHead className="p-2">{/* Actions column - no filter */}</TableHead>
      </TableRow>

      {/* Header Row with Sort */}
      <TableRow>
        <TableHead>
          <Button variant="ghost" onClick={() => handleSort("name")} className="h-auto p-0 font-semibold hover:bg-transparent">
            Vendor Name
            {getSortIcon("name")}
          </Button>
        </TableHead>
        <TableHead>
          <Button variant="ghost" onClick={() => handleSort("location")} className="h-auto p-0 font-semibold hover:bg-transparent">
            Location
            {getSortIcon("location")}
          </Button>
        </TableHead>
        <TableHead>
          <Button variant="ghost" onClick={() => handleSort("contact")} className="h-auto p-0 font-semibold hover:bg-transparent">
            Contact
            {getSortIcon("contact")}
          </Button>
        </TableHead>
        <TableHead>
          <Button variant="ghost" onClick={() => handleSort("vatNumber")} className="h-auto p-0 font-semibold hover:bg-transparent">
            VAT Number
            {getSortIcon("vatNumber")}
          </Button>
        </TableHead>
        <TableHead>
          <Button variant="ghost" onClick={() => handleSort("invoiceCount")} className="h-auto p-0 font-semibold hover:bg-transparent">
            Invoices
            {getSortIcon("invoiceCount")}
          </Button>
        </TableHead>
        <TableHead className="text-right">
          <Button variant="ghost" onClick={() => handleSort("totalAmount")} className="h-auto p-0 font-semibold hover:bg-transparent">
            Total Amount
            {getSortIcon("totalAmount")}
          </Button>
        </TableHead>
        <TableHead>
          <Button variant="ghost" onClick={() => handleSort("invoiceKind")} className="h-auto p-0 font-semibold hover:bg-transparent">
            Invoice Kind
            {getSortIcon("invoiceKind")}
          </Button>
        </TableHead>
        <TableHead className="w-[50px]">Actions</TableHead>
      </TableRow>
    </>
  );
}
