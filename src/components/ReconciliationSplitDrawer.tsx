import React, { useState } from "react";
import { trpc } from "@/utils/trpc";
import { Drawer, DrawerContent, DrawerDescription, DrawerHeader, DrawerTitle } from "@/components/ui/drawer";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import NextLink from "next/link";
import {
  ChevronLeft,
  ChevronRight,
  Calendar,
  Building,
  Receipt,
  Euro,
  ArrowUpDown,
  Activity,
  TrendingUp,
  Wallet,
  Copy,
  Check,
  ExternalLink,
  Link,
} from "lucide-react";
import { currency } from "@/modules/core/currency";
import { AccountType } from "@/prisma/generated";

interface ReconciliationSplitDrawerProps {
  invoiceId: string | null;
  transferId: string | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onNavigateNext?: () => void;
  onNavigatePrevious?: () => void;
  hasNext?: boolean;
  hasPrevious?: boolean;
}

export function ReconciliationSplitDrawer({
  invoiceId,
  transferId,
  open,
  onOpenChange,
  onNavigateNext,
  onNavigatePrevious,
  hasNext,
  hasPrevious,
}: ReconciliationSplitDrawerProps) {
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const [vendorPopoverOpen, setVendorPopoverOpen] = useState(false);

  // Fetch invoice details
  const { data: invoice, isLoading: invoiceLoading, error: invoiceError } = trpc.invoices.getById.useQuery({ id: invoiceId! }, { enabled: !!invoiceId });

  // Fetch transfer details to get transaction ID
  const { data: transfer, isLoading: transferLoading } = trpc.transfers.getById.useQuery({ id: transferId! }, { enabled: !!transferId });

  // Fetch transaction details using the transaction ID from the transfer
  const {
    data: transaction,
    isLoading: transactionLoading,
    error: transactionError,
  } = trpc.transactions.getById.useQuery({ id: transfer?.transactionId! }, { enabled: !!transfer?.transactionId });

  // Fetch related transactions if part of a group
  const { data: groupTransactions } = trpc.transactions.getByGroupId.useQuery(
    { transactionGroupId: transaction?.transactionGroupId! },
    { enabled: !!transaction?.transactionGroupId }
  );

  // Fetch detailed match score
  const { data: detailedScore, isLoading: scoreLoading } = trpc.reconciliation.getDetailedMatchScore.useQuery(
    { invoiceId: invoiceId!, transferId: transferId! },
    { enabled: !!invoiceId && !!transferId }
  );

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("de-DE", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    }).format(date);
  };

  const formatCurrency = (amount: any) => {
    return currency.formatMonetary(amount, "EUR");
  };

  const copyToClipboard = async (text: string, id: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedId(id);
      setTimeout(() => setCopiedId(null), 2000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  const getTransactionTypeIcon = (transaction: any) => {
    if (transaction.transfer) {
      return <ArrowUpDown className="h-5 w-5 text-blue-600" />;
    }
    if (transaction.trade) {
      return <TrendingUp className="h-5 w-5 text-green-600" />;
    }
    return <Activity className="h-5 w-5 text-gray-600" />;
  };

  const getTransactionTypeLabel = (transaction: any) => {
    if (transaction.transfer) return "Transfer";
    if (transaction.trade) return "Trade";
    return "Transaction";
  };

  const getAccountTypeIcon = (type: AccountType) => {
    switch (type) {
      case AccountType.BANK_ACCOUNT:
        return <Building className="h-4 w-4" />;
      case AccountType.CREDIT_CARD:
        return <Receipt className="h-4 w-4" />;
      case AccountType.WALLET:
        return <Wallet className="h-4 w-4" />;
      case AccountType.EXCHANGE_ACCOUNT:
        return <Activity className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  const getAccountTypeBadgeColor = (type: AccountType) => {
    switch (type) {
      case AccountType.BANK_ACCOUNT:
        return "bg-blue-100 text-blue-800";
      case AccountType.CREDIT_CARD:
        return "bg-purple-100 text-purple-800";
      case AccountType.WALLET:
        return "bg-green-100 text-green-800";
      case AccountType.EXCHANGE_ACCOUNT:
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Drawer open={open} onOpenChange={onOpenChange} direction="right">
      <DrawerContent className="h-full !max-w-none !w-[90%]">
        <DrawerHeader className="border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div>
                <DrawerTitle className="text-xl">Reconciliation Match Details</DrawerTitle>
                <DrawerDescription>
                  {invoice && transaction ? `Invoice ${invoice.invoiceReference} ↔ Transaction ${transaction.id.slice(0, 8)}...` : "Loading match details..."}
                </DrawerDescription>
              </div>
            </div>

            {/* Navigation Controls */}
            <div className="flex items-center gap-2">
              {(hasNext || hasPrevious) && (
                <>
                  <Button variant="outline" size="sm" onClick={onNavigatePrevious} disabled={!hasPrevious} className="flex items-center gap-1">
                    <ChevronLeft className="h-4 w-4" />
                    Previous
                  </Button>
                  <Button variant="outline" size="sm" onClick={onNavigateNext} disabled={!hasNext} className="flex items-center gap-1">
                    Next
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </>
              )}
            </div>
          </div>
        </DrawerHeader>

        {/* Match Details Section */}
        {detailedScore && !scoreLoading && (
          <div className="border-b p-6 bg-muted/30">
            <div className="flex items-center gap-2 mb-4">
              <Link className="h-5 w-5 text-green-600" />
              <h3 className="text-lg font-semibold">Match Analysis</h3>
              <Badge
                variant="default"
                className={`ml-auto ${
                  detailedScore.totalScore >= 80
                    ? "bg-green-100 text-green-800"
                    : detailedScore.totalScore >= 60
                    ? "bg-yellow-100 text-yellow-800"
                    : "bg-red-100 text-red-800"
                }`}
              >
                {detailedScore.totalScore.toFixed(0)}% Confidence
              </Badge>
            </div>

            <div className="grid grid-cols-3 gap-4">
              {/* Amount Match */}
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Euro className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium">Amount</span>
                    </div>
                    <Badge variant="secondary">
                      {detailedScore.breakdown.amount.score.toFixed(0)}/{detailedScore.breakdown.amount.maxScore}
                    </Badge>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${(detailedScore.breakdown.amount.score / detailedScore.breakdown.amount.maxScore) * 100}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-muted-foreground">{detailedScore.breakdown.amount.details}</p>
                </CardContent>
              </Card>

              {/* Date Match */}
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium">Date</span>
                    </div>
                    <Badge variant="secondary">
                      {detailedScore.breakdown.date.score.toFixed(0)}/{detailedScore.breakdown.date.maxScore}
                    </Badge>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div
                      className="bg-green-600 h-2 rounded-full"
                      style={{ width: `${(detailedScore.breakdown.date.score / detailedScore.breakdown.date.maxScore) * 100}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-muted-foreground">{detailedScore.breakdown.date.details}</p>
                </CardContent>
              </Card>

              {/* Vendor Match */}
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Building className="h-4 w-4 text-purple-600" />
                      <span className="text-sm font-medium">Vendor</span>
                    </div>
                    <Badge variant="secondary">
                      {detailedScore.breakdown.vendor.score.toFixed(0)}/{detailedScore.breakdown.vendor.maxScore}
                    </Badge>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div
                      className="bg-purple-600 h-2 rounded-full"
                      style={{ width: `${(detailedScore.breakdown.vendor.score / detailedScore.breakdown.vendor.maxScore) * 100}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-muted-foreground">{detailedScore.breakdown.vendor.details}</p>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        <div className="flex-1 overflow-hidden flex">
          {/* Invoice Details Section - Left Side */}
          <div className="w-1/2 border-r overflow-y-auto p-6 space-y-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Receipt className="h-5 w-5 text-blue-600" />
                <h3 className="text-lg font-semibold">Invoice Details</h3>
              </div>
              {invoiceId && (
                <NextLink href={`/invoices?invoiceId=${invoiceId}`} target="_blank" rel="noopener noreferrer">
                  <Button variant="outline" size="sm" className="flex items-center gap-2">
                    <ExternalLink className="h-4 w-4" />
                    Open Invoice
                  </Button>
                </NextLink>
              )}
            </div>

            {invoiceLoading && (
              <div className="flex items-center justify-center py-8">
                <div className="text-muted-foreground">Loading invoice details...</div>
              </div>
            )}

            {invoiceError && (
              <div className="flex items-center justify-center py-8">
                <div className="text-destructive">Failed to load invoice: {invoiceError.message}</div>
              </div>
            )}

            {invoice && (
              <>
                {/* Invoice Header */}
                <Card>
                  <CardContent className="space-y-6">
                    {/* Date & Vendor Row */}
                    <div className="flex flex-col sm:flex-row sm:items-start gap-6">
                      {/* Date */}
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Date</label>
                        <p className="flex items-center gap-2 text-lg font-semibold">
                          <Calendar className="h-5 w-5 text-blue-600" />
                          {formatDate(invoice.date)}
                        </p>
                      </div>

                      {/* Vendor */}
                      <div className="flex-1">
                        <label className="text-sm font-medium text-muted-foreground">Vendor</label>
                        <div className="flex items-center gap-2">
                          <Popover open={vendorPopoverOpen} onOpenChange={setVendorPopoverOpen}>
                            <PopoverTrigger asChild>
                              <button
                                className="flex items-center gap-2 text-lg font-semibold hover:text-blue-600 transition-colors"
                                onMouseEnter={() => setVendorPopoverOpen(true)}
                                onMouseLeave={() => setVendorPopoverOpen(false)}
                              >
                                <Building className="h-5 w-5 text-blue-600" />
                                {invoice.vendor?.name || "Unknown Vendor"}
                              </button>
                            </PopoverTrigger>
                            <PopoverContent className="w-80" side="bottom" align="start">
                              <div className="space-y-3">
                                <div>
                                  <h4 className="font-semibold">{invoice.vendor?.name || "Unknown Vendor"}</h4>
                                </div>
                                {invoice.vendor?.street && (
                                  <div>
                                    <label className="text-xs font-medium text-muted-foreground">Address</label>
                                    <p className="text-sm">
                                      {invoice.vendor.street} {invoice.vendor.houseNumber}
                                      {invoice.vendor.zip && `, ${invoice.vendor.zip}`} {invoice.vendor.city}
                                      {invoice.vendor.country && `, ${invoice.vendor.country}`}
                                    </p>
                                  </div>
                                )}
                                {invoice.vendor?.email && (
                                  <div>
                                    <label className="text-xs font-medium text-muted-foreground">Email</label>
                                    <p className="text-sm">{invoice.vendor.email}</p>
                                  </div>
                                )}
                                {invoice.vendor?.phone && (
                                  <div>
                                    <label className="text-xs font-medium text-muted-foreground">Phone</label>
                                    <p className="text-sm">{invoice.vendor.phone}</p>
                                  </div>
                                )}
                              </div>
                            </PopoverContent>
                          </Popover>
                        </div>
                      </div>
                    </div>

                    {/* Amounts */}
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Total Amount</label>
                      <div className="space-y-1">
                        <p className="text-xl font-bold">{formatCurrency(invoice.amountGross)}</p>
                        {(invoice.amountVat as any) > 0 && <p className="text-md text-muted-foreground">Net: {formatCurrency(invoice.amountNet)}</p>}
                        {(invoice.amountVat as any) > 0 && <p className="text-md text-muted-foreground">Tax: {formatCurrency(invoice.amountVat)}</p>}
                        {invoice.sourceAmountGross && invoice.currencyCode !== "EUR" && (
                          <p className="text-md text-muted-foreground">Original: {currency.formatMonetary(invoice.sourceAmountGross, invoice.currencyCode)}</p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Additional Invoice Info */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Additional Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 gap-4 text-sm">
                      <div>
                        <label className="text-xs font-medium text-muted-foreground">Invoice Reference</label>
                        <p className="font-mono">{invoice.invoiceReference}</p>
                      </div>
                      <div>
                        <label className="text-xs font-medium text-muted-foreground">Currency</label>
                        <Badge variant="secondary" className="mt-1">
                          {invoice.currencyCode}
                        </Badge>
                      </div>
                      <div>
                        <label className="text-xs font-medium text-muted-foreground">External ID</label>
                        <Badge
                          variant="secondary"
                          className="mt-1 cursor-pointer hover:bg-secondary/80 transition-colors"
                          onClick={() => copyToClipboard(invoice.importItem.externalId, "invoice-external-id")}
                        >
                          {copiedId === "invoice-external-id" ? <Check className="h-3 w-3 mr-1" /> : <Copy className="h-3 w-3 mr-1" />}
                          {invoice.importItem.externalId}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </div>

          {/* Transaction Details Section - Right Side */}
          <div className="w-1/2 overflow-y-auto p-6 space-y-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <ArrowUpDown className="h-5 w-5 text-green-600" />
                <h3 className="text-lg font-semibold">Transaction Details</h3>
              </div>
              {transaction && (
                <NextLink href={`/transactions?transactionId=${transaction.id}`} target="_blank" rel="noopener noreferrer">
                  <Button variant="outline" size="sm" className="flex items-center gap-2">
                    <ExternalLink className="h-4 w-4" />
                    Open Transaction
                  </Button>
                </NextLink>
              )}
            </div>

            {transactionLoading && (
              <div className="flex items-center justify-center py-8">
                <div className="text-muted-foreground">Loading transaction details...</div>
              </div>
            )}

            {transactionError && (
              <div className="flex items-center justify-center py-8">
                <div className="text-destructive">Failed to load transaction: {transactionError.message}</div>
              </div>
            )}

            {transaction && (
              <>
                {/* Transaction Header */}
                <Card>
                  <CardContent className="space-y-6">
                    {/* Date & Type Row */}
                    <div className="flex flex-col sm:flex-row sm:items-start gap-6">
                      {/* Date */}
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Date</label>
                        <p className="flex items-center gap-2 text-lg font-semibold">
                          <Calendar className="h-5 w-5 text-blue-600" />
                          {formatDate(transaction.executedAt)}
                        </p>
                      </div>

                      {/* Type */}
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Type</label>
                        <p className="flex items-center gap-2 text-lg font-semibold">
                          {getTransactionTypeIcon(transaction)}
                          {getTransactionTypeLabel(transaction)}
                        </p>
                      </div>
                    </div>

                    {/* Account */}
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Account</label>
                      <div className="flex items-center gap-2">
                        <Badge className={`${getAccountTypeBadgeColor(transaction.account.type)} flex items-center gap-1`}>
                          {getAccountTypeIcon(transaction.account.type)}
                          {transaction.account.type}
                        </Badge>
                        <span className="font-medium">{transaction.account.name}</span>
                      </div>
                    </div>

                    {/* Transfer Details */}
                    {transaction.transfer && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Transfer Amount</label>
                        <p className="text-xl font-bold">{currency.formatMonetary(transaction.transfer.amount, transaction.transfer.currencyCode)}</p>
                        {transaction.transfer.counterparty && (
                          <p className="text-sm text-muted-foreground mt-1">Counterparty: {transaction.transfer.counterparty}</p>
                        )}
                        {transaction.transfer.description && <p className="text-sm text-muted-foreground">Description: {transaction.transfer.description}</p>}
                      </div>
                    )}

                    {/* Trade Details */}
                    {transaction.trade && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Trade</label>
                        <div className="space-y-2">
                          <p className="text-lg font-semibold">
                            {transaction.trade.amountFrom.toString()} {transaction.trade.tokenFrom} → {transaction.trade.amountTo.toString()}{" "}
                            {transaction.trade.tokenTo}
                          </p>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Related Transactions */}
                {groupTransactions && groupTransactions.length > 1 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Link className="h-5 w-5" />
                        Related Transactions ({groupTransactions.length - 1})
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {groupTransactions
                          .filter((groupTx) => groupTx.id !== transaction.id)
                          .map((groupTx) => (
                            <div key={groupTx.id} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                              <div className="flex-1">
                                <div className="flex items-center gap-2">
                                  {getTransactionTypeIcon(groupTx)}
                                  <span className="font-medium">{getTransactionTypeLabel(groupTx)}</span>
                                </div>
                                <p className="text-sm text-muted-foreground">{groupTx.executedAt.toLocaleDateString("de-DE")}</p>
                              </div>
                              <div className="flex items-center gap-3">
                                <div className="text-right">
                                  {groupTx.transfer && (
                                    <p className="font-medium">{currency.formatMonetary(groupTx.transfer.amount, groupTx.transfer.currencyCode)}</p>
                                  )}
                                  {groupTx.trade && (
                                    <p className="font-medium text-sm">
                                      {groupTx.trade.tokenFrom} → {groupTx.trade.tokenTo}
                                    </p>
                                  )}
                                </div>
                                <NextLink href={`/transactions?transactionId=${groupTx.id}`} target="_blank" rel="noopener noreferrer">
                                  <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700 h-8 w-8 p-0">
                                    <ExternalLink className="h-4 w-4" />
                                  </Button>
                                </NextLink>
                              </div>
                            </div>
                          ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Additional Transaction Info */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Additional Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 gap-4 text-sm">
                      <div>
                        <label className="text-xs font-medium text-muted-foreground">Transaction ID</label>
                        <Badge
                          variant="secondary"
                          className="mt-1 cursor-pointer hover:bg-secondary/80 transition-colors"
                          onClick={() => copyToClipboard(transaction.id, "transaction-id")}
                        >
                          {copiedId === "transaction-id" ? <Check className="h-3 w-3 mr-1" /> : <Copy className="h-3 w-3 mr-1" />}
                          {transaction.id.slice(0, 8)}...
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Metadata */}
                {transaction.metadata && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Metadata</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <pre className="text-sm bg-muted p-4 rounded-lg overflow-auto">{JSON.stringify(transaction.metadata, null, 2)}</pre>
                    </CardContent>
                  </Card>
                )}
              </>
            )}
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
}
