import * as React from "react";
import { ArrowUpDown, ArrowUp, ArrowDown } from "lucide-react";

import { Button } from "@/components/ui/button";
import { TableHead, TableRow } from "@/components/ui/table";

export interface SortConfig {
  field: string;
  direction: "asc" | "desc" | null;
}

interface InvoiceTableHeaderProps {
  // Sort props
  sortConfig?: SortConfig;
  onSortChange?: (sort: SortConfig) => void;
}

export function InvoiceTableHeader({ sortConfig, onSortChange }: InvoiceTableHeaderProps) {
  const handleSort = (field: string) => {
    if (!onSortChange) return;

    let direction: "asc" | "desc" | null = "asc";

    if (sortConfig?.field === field) {
      if (sortConfig.direction === "asc") {
        direction = "desc";
      } else if (sortConfig.direction === "desc") {
        direction = null;
      }
    }

    onSortChange({ field, direction });
  };

  const getSortIcon = (field: string) => {
    if (sortConfig?.field !== field || !sortConfig.direction) {
      return <ArrowUpDown className="ml-2 h-4 w-4" />;
    }

    return sortConfig.direction === "asc" ? <ArrowUp className="ml-2 h-4 w-4" /> : <ArrowDown className="ml-2 h-4 w-4" />;
  };

  return (
    <TableRow>
      <TableHead>
        <Button variant="ghost" onClick={() => handleSort("date")} className="h-auto p-0 font-semibold hover:bg-transparent">
          Date
          {getSortIcon("date")}
        </Button>
      </TableHead>
      <TableHead>
        <Button variant="ghost" onClick={() => handleSort("vendor")} className="h-auto p-0 font-semibold hover:bg-transparent">
          Vendor
          {getSortIcon("vendor")}
        </Button>
      </TableHead>
      <TableHead>
        <Button variant="ghost" onClick={() => handleSort("amount")} className="h-auto p-0 font-semibold hover:bg-transparent">
          Amount
          {getSortIcon("amount")}
        </Button>
      </TableHead>
      <TableHead>Source Amount</TableHead>
      <TableHead>VAT</TableHead>
      <TableHead>Import Type</TableHead>
      <TableHead>Invoice Ref</TableHead>
      <TableHead>Reconciliation</TableHead>
      <TableHead className="text-right">Actions</TableHead>
    </TableRow>
  );
}
