import React, { useState, useEffect } from "react";
import { trpc } from "@/utils/trpc";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ChevronLeft, ChevronRight, Activity, Wallet, TrendingUp, ArrowUpDown, ExternalLink, ArrowUp, ArrowDown } from "lucide-react";
import { TransactionDetailsDrawer } from "@/components/TransactionDetailsDrawer";
import { currency } from "@/modules/core/currency";
import { SearchInput } from "@/components/SearchInput";
import { DateRange, DateRangePicker } from "@/components/ui/date-range-picker";
import { Amount<PERSON><PERSON>e, AmountRangeFilter } from "@/components/ui/amount-range-filter";
import { MultiAccountFilter } from "@/components/ui/account-filter";
import { useRouter } from "next/router";
import { DashboardLayout } from "@/components/DashboardLayout";
import { AccountType } from "@/prisma/generated";
import { CopyablePublicKey } from "@/components/AccountDataTable";

export interface SortConfig {
  field: string;
  direction: "asc" | "desc" | null;
}

export default function TransactionsPage() {
  const [limit, setLimit] = useState(20);
  const [cursor, setCursor] = useState<string | undefined>(undefined);
  const [cursors, setCursors] = useState<string[]>([]);
  const [selectedTransactionId, setSelectedTransactionId] = useState<string | null>(null);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [selectedAccountIds, setSelectedAccountIds] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [selectedType, setSelectedType] = useState<string | undefined>(undefined);
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [amountRange, setAmountRange] = useState<AmountRange | undefined>(undefined);
  const [reconciledFilter, setReconciledFilter] = useState<string | undefined>(undefined);
  const [sortConfig, setSortConfig] = useState<SortConfig>({ field: "executedAt", direction: "desc" });

  // Fetch transaction stats
  const stats = trpc.transactions.getStats.useQuery();

  // Fetch filtered stats when filters are applied
  const hasFilters =
    selectedAccountIds.length > 0 ||
    !!searchTerm.trim() ||
    !!selectedType ||
    !!dateRange?.from ||
    !!dateRange?.to ||
    !!amountRange?.min ||
    !!amountRange?.max ||
    !!reconciledFilter;
  const filteredStats = trpc.transactions.getFilteredStats.useQuery(
    {
      ...(selectedAccountIds.length > 0 && { accountIds: selectedAccountIds }),
      ...(searchTerm.trim() && { search: searchTerm.trim() }),
      ...(selectedType && { type: selectedType as "transfer" | "trade" | "crypto_expense" }),
      ...(dateRange?.from && { dateFrom: dateRange.from.toISOString() }),
      ...(dateRange?.to && { dateTo: dateRange.to.toISOString() }),
      ...(amountRange?.min !== undefined && { amountMin: amountRange.min }),
      ...(amountRange?.max !== undefined && { amountMax: amountRange.max }),
      ...(reconciledFilter && { reconciled: reconciledFilter === "reconciled" }),
    },
    {
      enabled: hasFilters, // Only fetch when filters are applied
    }
  );

  // Fetch accounts for filter - get all accounts without pagination
  const accounts = trpc.accounts.getAll.useQuery({ limit: 500 });

  // Fetch transactions with pagination
  const transactions = trpc.transactions.getAll.useQuery({
    limit,
    ...(cursor && { cursor }),
    ...(selectedAccountIds.length > 0 && { accountIds: selectedAccountIds }),
    ...(searchTerm.trim() && { search: searchTerm.trim() }),
    ...(selectedType && { type: selectedType as "transfer" | "trade" | "crypto_expense" }),
    ...(dateRange?.from && { dateFrom: dateRange.from.toISOString() }),
    ...(dateRange?.to && { dateTo: dateRange.to.toISOString() }),
    ...(amountRange?.min !== undefined && { amountMin: amountRange.min }),
    ...(amountRange?.max !== undefined && { amountMax: amountRange.max }),
    ...(reconciledFilter && { reconciled: reconciledFilter === "reconciled" }),
    ...(sortConfig.direction && {
      sortField: sortConfig.field as "executedAt" | "account" | "type" | "amount",
      sortDirection: sortConfig.direction,
    }),
  });

  // Router for URL query handling
  const router = useRouter();

  // Handle transactionId from URL query parameters
  useEffect(() => {
    if (router.query.transactionId && typeof router.query.transactionId === "string") {
      setSelectedTransactionId(router.query.transactionId);
      setDrawerOpen(true);
    }
  }, [router.query.transactionId]);

  // Reset pagination when any filter changes
  useEffect(() => {
    setCursor(undefined);
    setCursors([]);
  }, [searchTerm, selectedAccountIds, selectedType, dateRange, amountRange, reconciledFilter, sortConfig]);

  const handleNextPage = () => {
    if (transactions.data?.nextCursor) {
      setCursors((prev) => [...prev, cursor || ""]);
      setCursor(transactions.data.nextCursor);
    }
  };

  const handlePreviousPage = () => {
    if (cursors.length > 0) {
      const newCursors = [...cursors];
      const previousCursor = newCursors.pop();
      setCursors(newCursors);
      setCursor(previousCursor === "" ? undefined : previousCursor);
    }
  };

  const handleTransactionClick = (transactionId: string) => {
    setSelectedTransactionId(transactionId);
    setDrawerOpen(true);
    router.push(`/transactions?transactionId=${transactionId}`, undefined, { shallow: true });
  };

  const handleDrawerOpenChange = (open: boolean) => {
    setDrawerOpen(open);
    if (!open) {
      setSelectedTransactionId(null);
      router.push("/transactions", undefined, { shallow: true });
    }
  };

  // Navigation between transactions
  const currentIndex = transactions.data?.transactions?.findIndex((t) => t.id === selectedTransactionId) ?? -1;
  const hasNext = currentIndex >= 0 && currentIndex < (transactions.data?.transactions?.length ?? 0) - 1;
  const hasPrevious = currentIndex > 0;

  const handleNavigateNext = () => {
    if (hasNext && transactions.data?.transactions) {
      const nextTransaction = transactions.data.transactions[currentIndex + 1];
      handleTransactionClick(nextTransaction.id);
    }
  };

  const handleNavigatePrevious = () => {
    if (hasPrevious && transactions.data?.transactions) {
      const previousTransaction = transactions.data.transactions[currentIndex - 1];
      handleTransactionClick(previousTransaction.id);
    }
  };

  // Sorting functions
  const handleSort = (field: string) => {
    let direction: "asc" | "desc" | null = "asc";

    if (sortConfig?.field === field) {
      if (sortConfig.direction === "asc") {
        direction = "desc";
      } else if (sortConfig.direction === "desc") {
        direction = null;
      }
    }

    setSortConfig({ field, direction });
  };

  const getSortIcon = (field: string) => {
    if (sortConfig?.field !== field || !sortConfig.direction) {
      return <ArrowUpDown className="ml-2 h-4 w-4" />;
    }

    return sortConfig.direction === "asc" ? <ArrowUp className="ml-2 h-4 w-4" /> : <ArrowDown className="ml-2 h-4 w-4" />;
  };

  const getTransactionTypeBadge = (transaction: any) => {
    if (transaction.transfer) {
      return (
        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
          Transfer
        </Badge>
      );
    }
    if (transaction.trade) {
      return (
        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
          Trade
        </Badge>
      );
    }
    if (transaction.cryptoExpense) {
      // Show specific crypto expense type
      const expenseType = transaction.cryptoExpense.type;
      const typeLabels = {
        BUYBACK: "Buyback",
        FLOOR_SWEEP: "Floor Sweep",
        LIQUIDITY_POOL: "LP Action",
        OTHER: "Crypto Expense",
      };
      const label = typeLabels[expenseType as keyof typeof typeLabels] || "Crypto Expense";

      return (
        <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
          {label}
        </Badge>
      );
    }
    return <Badge variant="outline">Unknown</Badge>;
  };

  const getAccountTypeLabel = (type: AccountType) => {
    switch (type) {
      case "WALLET":
        return "Wallet";
      case "BANK_ACCOUNT":
        return "Bank Account";
      case "EXCHANGE_ACCOUNT":
        return "Exchange Account";
      case "CREDIT_CARD":
        return "Credit Card";
      default:
        return type;
    }
  };

  const formatTransactionAmount = (transaction: any) => {
    if (transaction.transfer) {
      return currency.formatMonetary(transaction.transfer.amount, transaction.transfer.currencyCode);
    }
    if (transaction.trade) {
      return `${currency.formatMonetary(transaction.trade.amountFrom, transaction.trade.tokenFrom)} → ${currency.formatMonetary(
        transaction.trade.amountTo,
        transaction.trade.tokenTo
      )}`;
    }
    if (transaction.cryptoExpense) {
      // Show the main amount field as EUR equivalent
      const usdAmount = Number(transaction.cryptoExpense.amountUsd);
      return currency.formatMonetary(usdAmount, "EUR");
    }
    return "N/A";
  };

  const getTransactionDescription = (transaction: any) => {
    if (transaction.transfer) {
      return transaction.transfer.description || transaction.transfer.counterparty || "Transfer";
    }
    if (transaction.trade) {
      return transaction.trade.description || `${transaction.trade.tokenFrom} → ${transaction.trade.tokenTo}`;
    }
    if (transaction.cryptoExpense) {
      const expense = transaction.cryptoExpense;
      if (expense.type === "FLOOR_SWEEP" && expense.floorSweep) {
        return `${expense.type}: ${expense.floorSweep.collectionName || expense.floorSweep.collectionId}`;
      }
      if (expense.type === "LIQUIDITY_POOL" && expense.liquidityPool) {
        return `${expense.type}: ${expense.liquidityPool.action} (${expense.liquidityPool.tokenASymbol}/${expense.liquidityPool.tokenBSymbol})`;
      }
      if (expense.type === "BUYBACK" && expense.buyback) {
        return `${expense.type}: ${expense.buyback.swapFromSymbol} → ${expense.buyback.swapToSymbol}`;
      }
      return expense.type;
    }
    return "Unknown transaction";
  };

  const getReconciliationStatus = (transaction: any) => {
    // Only transfers can be reconciled
    if (!transaction.transfer) {
      return null;
    }

    const reconciliations = transaction.transfer.reconciliations || [];
    const activeReconciliations = reconciliations.filter((r: any) => r.status === "AUTO_MATCHED" || r.status === "MANUALLY_MATCHED");

    if (activeReconciliations.length > 0) {
      const reconciliation = activeReconciliations[0];
      return (
        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
          {reconciliation.status === "AUTO_MATCHED" ? "Auto" : "Manual"}
        </Badge>
      );
    }

    return (
      <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
        Unreconciled
      </Badge>
    );
  };

  return (
    <DashboardLayout title="Transactions">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Transactions</h1>
            <p className="text-muted-foreground">View and manage all transactions across accounts</p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.data?.total || 0}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Transfers</CardTitle>
              <ArrowUpDown className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.data?.withTransfers || 0}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Trades</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.data?.withTrades || 0}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Crypto Expenses</CardTitle>
              <Wallet className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.data?.withCryptoExpenses || 0}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Other Actions</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.data?.withLiquidityActions || 0}</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters Section */}
        <div className="space-y-4">
          {/* Search Input */}
          <SearchInput value={searchTerm} onChange={setSearchTerm} placeholder="Search transactions..." className="w-64" />

          {/* First Row: Account and Type Filters */}
          <div className="flex items-center gap-4">
            {/* Account Filter */}
            <MultiAccountFilter
              accounts={accounts.data?.items || []}
              selectedAccountIds={selectedAccountIds}
              onAccountChange={setSelectedAccountIds}
              placeholder="All accounts"
              getAccountTypeLabel={(type: string) => getAccountTypeLabel(type as AccountType)}
              className="w-64"
            />

            {/* Transaction Type Filter */}
            <Select value={selectedType || "all"} onValueChange={(value) => setSelectedType(value === "all" ? undefined : value)}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All types</SelectItem>
                <SelectItem value="transfer">Transfers</SelectItem>
                <SelectItem value="trade">Trades</SelectItem>
                <SelectItem value="crypto_expense">Crypto Expenses</SelectItem>
              </SelectContent>
            </Select>

            {/* Reconciled Filter */}
            <Select value={reconciledFilter || "all"} onValueChange={(value) => setReconciledFilter(value === "all" ? undefined : value)}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All transactions" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All transactions</SelectItem>
                <SelectItem value="reconciled">Reconciled only</SelectItem>
                <SelectItem value="unreconciled">Unreconciled only</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Second Row: Date and Amount Filters */}
          <div className="flex items-center gap-4">
            {/* Date Range Filter */}
            <DateRangePicker dateRange={dateRange} onDateRangeChange={setDateRange} placeholder="Filter by date" className="w-64" />

            {/* Amount Range Filter */}
            <AmountRangeFilter amountRange={amountRange} onAmountRangeChange={setAmountRange} placeholder="Filter by USD amount" className="w-64" />
          </div>
        </div>

        {/* Pagination Controls */}
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            {transactions.data?.transactions ? `Showing ${transactions.data.transactions.length} transactions` : "Loading..."}
          </div>
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Rows per page:</span>
              <Select value={limit.toString()} onValueChange={(value) => setLimit(parseInt(value))}>
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button variant="outline" size="sm" onClick={handlePreviousPage} disabled={cursors.length === 0}>
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <Button variant="outline" size="sm" onClick={handleNextPage} disabled={!transactions.data?.nextCursor}>
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Filtered Statistics */}
        {hasFilters && (filteredStats.data?.total ?? 0) > 0 && (
          <Card className="py-2 gap-0">
            <CardHeader>
              <CardTitle className="text-md text-muted-foreground">Statistics for the current filters</CardTitle>
            </CardHeader>
            <CardContent>
              {filteredStats.isLoading ? (
                <div className="text-center text-muted-foreground">Loading filtered stats...</div>
              ) : filteredStats.data ? (
                <div className="flex gap-6">
                  <div className="flex items-center gap-2">
                    <div className="text-sm text-muted-foreground">Total Transactions</div>
                    <div className="text-sm font-medium">{filteredStats.data.total}</div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="text-sm text-muted-foreground">Transfers</div>
                    <div className="text-sm font-medium">{filteredStats.data.withTransfers}</div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="text-sm text-muted-foreground">Trades</div>
                    <div className="text-sm font-medium">{filteredStats.data.withTrades}</div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="text-sm text-muted-foreground">Crypto Expenses</div>
                    <div className="text-sm font-medium">{filteredStats.data.withCryptoExpenses}</div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="text-sm text-muted-foreground">Other Actions</div>
                    <div className="text-sm font-medium">{filteredStats.data.withLiquidityActions}</div>
                  </div>
                </div>
              ) : (
                <div className="text-center text-destructive">Failed to load filtered stats</div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Transactions Data Table */}
        <Card>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>
                    <Button variant="ghost" onClick={() => handleSort("executedAt")} className="h-auto p-0 font-semibold hover:bg-transparent">
                      Date
                      {getSortIcon("executedAt")}
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button variant="ghost" onClick={() => handleSort("account")} className="h-auto p-0 font-semibold hover:bg-transparent">
                      Account
                      {getSortIcon("account")}
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button variant="ghost" onClick={() => handleSort("type")} className="h-auto p-0 font-semibold hover:bg-transparent">
                      Type
                      {getSortIcon("type")}
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button variant="ghost" onClick={() => handleSort("amount")} className="h-auto p-0 font-semibold hover:bg-transparent">
                      Amount
                      {getSortIcon("amount")}
                    </Button>
                  </TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Counterparty</TableHead>
                  <TableHead>Reconciled</TableHead>
                  <TableHead>Group</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              {transactions.isLoading ? (
                <TableBody>
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="text-muted-foreground">Loading transactions...</div>
                    </TableCell>
                  </TableRow>
                </TableBody>
              ) : transactions.data ? (
                <>
                  {transactions.data.transactions?.length === 0 ? (
                    <TableBody>
                      <TableRow>
                        <TableCell colSpan={8} className="text-center py-8">
                          <div className="text-muted-foreground">No transactions found</div>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  ) : (
                    <TableBody>
                      {transactions.data?.transactions?.map((transaction) => (
                        <TableRow key={transaction.id} className="cursor-pointer hover:bg-muted/50" onClick={() => handleTransactionClick(transaction.id)}>
                          <TableCell className="text-sm text-muted-foreground">{transaction.executedAt.toLocaleDateString("de-DE")}</TableCell>
                          <TableCell className="max-w-xs">
                            <div className="truncate text-sm">
                              <div className="font-medium">{transaction.account?.name || "Unknown Account"}</div>
                              <div className="text-xs text-muted-foreground">
                                {transaction.account.type === "WALLET" && transaction.account.publicKey ? (
                                  <CopyablePublicKey publicKey={transaction.account.publicKey} />
                                ) : (
                                  getAccountTypeLabel(transaction.account?.type || "WALLET")
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>{getTransactionTypeBadge(transaction)}</TableCell>
                          <TableCell className="text-sm">
                            <div className="font-medium">{formatTransactionAmount(transaction)}</div>
                          </TableCell>
                          <TableCell className="max-w-xs">
                            <div className="truncate text-sm">{getTransactionDescription(transaction)}</div>
                          </TableCell>
                          <TableCell className="max-w-xs">
                            <div className="truncate text-sm">{transaction.transfer?.counterparty}</div>
                          </TableCell>
                          <TableCell className="text-sm">{getReconciliationStatus(transaction)}</TableCell>
                          <TableCell className="text-sm text-muted-foreground">
                            {transaction.transactionGroupId ? (
                              <Badge variant="outline" className="text-xs">
                                Group
                              </Badge>
                            ) : (
                              <span className="text-xs">Individual</span>
                            )}
                          </TableCell>
                          <TableCell className="text-center">
                            {transaction.solanaTransactionId && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  window.open(`https://solscan.io/tx/${transaction.solanaTransactionId}`, "_blank");
                                }}
                                className="h-8 w-8 p-0 hover:bg-muted"
                                title="View on Solscan"
                              >
                                <ExternalLink className="h-4 w-4" />
                              </Button>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  )}
                </>
              ) : (
                <TableBody>
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      <div className="text-destructive">Failed to load transactions: {transactions.error?.message}</div>
                    </TableCell>
                  </TableRow>
                </TableBody>
              )}
            </Table>
          </CardContent>
        </Card>

        {/* Transaction Details Drawer */}
        <TransactionDetailsDrawer
          transactionId={selectedTransactionId}
          open={drawerOpen}
          onOpenChange={handleDrawerOpenChange}
          onNavigateNext={handleNavigateNext}
          onNavigatePrevious={handleNavigatePrevious}
          hasNext={hasNext}
          hasPrevious={hasPrevious}
        />
      </div>
    </DashboardLayout>
  );
}
