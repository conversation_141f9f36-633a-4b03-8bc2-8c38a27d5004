import { trpc } from "@/utils/trpc";
import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { RefreshCw, CheckCircle, AlertCircle, Link, Unlink, ExternalLink } from "lucide-react";
import NextLink from "next/link";
import { DashboardLayout } from "@/components/DashboardLayout";
import { currency } from "@/modules/core/currency";
import { ReconciliationStatus } from "@/prisma/generated";
import { differenceInDays } from "date-fns";
import { toast } from "sonner";
import { ReconciliationSplitDrawer } from "@/components/ReconciliationSplitDrawer";

// Helper functions for calculating differences
function calculateAmountDifference(invoiceAmount: number, transferAmount: number): number {
  return Math.abs(invoiceAmount - transferAmount);
}

function calculateDateDifference(invoiceDate: Date, transferDate: Date): number {
  return Math.abs(differenceInDays(invoiceDate, transferDate));
}

function getVendorMatchingPercentage(detailedScore: { breakdown?: { vendor?: { score: number; maxScore: number } } }): number {
  if (!detailedScore?.breakdown?.vendor) return 0;
  return (detailedScore.breakdown.vendor.score / detailedScore.breakdown.vendor.maxScore) * 100;
}

export default function ReconciliationPage() {
  const [activeTab, setActiveTab] = useState("overview");

  // Split drawer state
  const [splitDrawerOpen, setSplitDrawerOpen] = useState(false);
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<string | null>(null);
  const [selectedTransferId, setSelectedTransferId] = useState<string | null>(null);
  const [currentMatchIndex, setCurrentMatchIndex] = useState(0);

  // Fetch reconciliation stats
  const stats = trpc.reconciliation.getStats.useQuery();

  // Fetch reconciliations
  const reconciliations = trpc.reconciliation.getAll.useQuery({ limit: 50 });

  // Fetch potential matches
  const potentialMatches = trpc.reconciliation.getPotentialMatches.useQuery();

  // Fetch unmatched invoices
  const unmatchedInvoices = trpc.reconciliation.getUnmatchedInvoices.useQuery({ limit: 50 });

  // Fetch unmatched transfers
  const unmatchedTransfers = trpc.reconciliation.getUnmatchedTransfers.useQuery({ limit: 50 });

  console.log("rerender");

  // Handler for opening split drawer
  const handleMatchRowClick = (invoiceId: string, transferId: string, matchIndex: number) => {
    setSelectedInvoiceId(invoiceId);
    setSelectedTransferId(transferId);
    setCurrentMatchIndex(matchIndex);
    setSplitDrawerOpen(true);
  };

  // Navigation handlers for split drawer
  const handleNavigateNext = () => {
    if (potentialMatches.data && currentMatchIndex < potentialMatches.data.length - 1) {
      const nextMatch = potentialMatches.data[currentMatchIndex + 1];
      setSelectedInvoiceId(nextMatch.invoiceId);
      setSelectedTransferId(nextMatch.transferId);
      setCurrentMatchIndex(currentMatchIndex + 1);
    }
  };

  const handleNavigatePrevious = () => {
    if (potentialMatches.data && currentMatchIndex > 0) {
      const prevMatch = potentialMatches.data[currentMatchIndex - 1];
      setSelectedInvoiceId(prevMatch.invoiceId);
      setSelectedTransferId(prevMatch.transferId);
      setCurrentMatchIndex(currentMatchIndex - 1);
    }
  };

  // Keyboard navigation for split drawer
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!splitDrawerOpen) return;

      if (event.key === "ArrowRight") {
        event.preventDefault();
        handleNavigateNext();
      } else if (event.key === "ArrowLeft") {
        event.preventDefault();
        handleNavigatePrevious();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [splitDrawerOpen, handleNavigateNext, handleNavigatePrevious]);

  // Mutations
  const autoReconcileMutation = trpc.reconciliation.autoReconcile.useMutation({
    onSuccess: (data) => {
      toast.success(`Auto-matched ${data.matchedCount} items`);
      // Refetch all data
      stats.refetch();
      reconciliations.refetch();
      potentialMatches.refetch();
      unmatchedInvoices.refetch();
      unmatchedTransfers.refetch();
    },
    onError: (error) => {
      toast.error(`Auto reconciliation failed: ${error.message}`);
    },
  });

  const manualMatchMutation = trpc.reconciliation.manualMatch.useMutation({
    onSuccess: () => {
      toast.success("Items matched successfully");
      // Refetch all data
      stats.refetch();
      reconciliations.refetch();
      unmatchedInvoices.refetch();
      unmatchedTransfers.refetch();
      potentialMatches.refetch();
    },
    onError: (error) => {
      toast.error(`Manual match failed: ${error.message}`);
    },
  });

  const manualUnmatchMutation = trpc.reconciliation.manualUnmatch.useMutation({
    onSuccess: () => {
      toast.success("Items unmatched successfully");
      // Refetch all data
      stats.refetch();
      reconciliations.refetch();
      unmatchedInvoices.refetch();
      unmatchedTransfers.refetch();
    },
    onError: (error) => {
      toast.error(`Manual unmatch failed: ${error.message}`);
    },
  });

  const handleAutoReconcile = () => {
    autoReconcileMutation.mutate();
  };

  const handleManualMatch = (invoiceId: string, transferId: string) => {
    manualMatchMutation.mutate({ invoiceId, transferId });
  };

  const handleManualUnmatch = (invoiceId: string, transferId: string) => {
    manualUnmatchMutation.mutate({ invoiceId, transferId });
  };

  const getStatusBadge = (status: ReconciliationStatus) => {
    switch (status) {
      case ReconciliationStatus.AUTO_MATCHED:
        return (
          <Badge variant="default" className="bg-green-100 text-green-800">
            Auto Matched
          </Badge>
        );
      case ReconciliationStatus.MANUALLY_MATCHED:
        return (
          <Badge variant="default" className="bg-blue-100 text-blue-800">
            Manual Match
          </Badge>
        );
      case ReconciliationStatus.MANUALLY_UNMATCHED:
        return <Badge variant="destructive">Unmatched</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const getConfidenceBadge = (score: string | number) => {
    const numScore = typeof score === "string" ? parseFloat(score) : score;
    if (numScore >= 80) {
      return (
        <Badge variant="default" className="bg-green-100 text-green-800">
          {score}%
        </Badge>
      );
    } else if (numScore >= 60) {
      return (
        <Badge variant="default" className="bg-yellow-100 text-yellow-800">
          {score}%
        </Badge>
      );
    } else {
      return <Badge variant="secondary">{score}%</Badge>;
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Reconciliation</h1>
            <p className="text-muted-foreground">Match invoices with transfers automatically and manually</p>
          </div>
          <div className="flex gap-2">
            <Button onClick={handleAutoReconcile} disabled={autoReconcileMutation.isPending} className="flex items-center gap-2">
              {autoReconcileMutation.isPending ? <RefreshCw className="h-4 w-4 animate-spin" /> : <CheckCircle className="h-4 w-4" />}
              Auto Reconcile
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                stats.refetch();
                reconciliations.refetch();
                unmatchedInvoices.refetch();
                unmatchedTransfers.refetch();
              }}
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        {stats.data && (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Invoices</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.data.totalInvoices}</div>
                <p className="text-xs text-muted-foreground">{stats.data.matchedInvoices} matched</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Transfers</CardTitle>
                <CheckCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.data.totalTransfers}</div>
                <p className="text-xs text-muted-foreground">{stats.data.matchedTransfers} matched</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Auto Matched</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.data.autoMatched}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Potential Matches</CardTitle>
                <AlertCircle className="h-4 w-4 text-yellow-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.data.potentialMatches}</div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="potential">Potential Matches</TabsTrigger>
            <TabsTrigger value="unmatched">Unmatched Items</TabsTrigger>
            <TabsTrigger value="matched">Matched Items</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Unmatched Invoices</CardTitle>
                  <CardDescription>Invoices that haven't been matched with transfers</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.data ? stats.data.totalInvoices - stats.data.matchedInvoices : 0}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Unmatched Transfers</CardTitle>
                  <CardDescription>Transfers that haven't been matched with invoices</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.data ? stats.data.totalTransfers - stats.data.matchedTransfers : 0}</div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="potential" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Potential Matches</CardTitle>
                <CardDescription>Suggested matches based on amount, date, and currency</CardDescription>
              </CardHeader>
              <CardContent>
                {potentialMatches.isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="h-6 w-6 animate-spin" />
                  </div>
                ) : potentialMatches.data && potentialMatches.data.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Invoice</TableHead>
                        <TableHead>Transfer</TableHead>
                        <TableHead>Match Details</TableHead>
                        <TableHead>Account</TableHead>
                        <TableHead>Confidence</TableHead>
                        <TableHead>Reason</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {potentialMatches.data.map((match, index) => (
                        <TableRow
                          key={`${match.invoiceId}-${match.transferId}`}
                          className="cursor-pointer hover:bg-muted/50 transition-colors"
                          onClick={() => handleMatchRowClick(match.invoiceId, match.transferId, index)}
                        >
                          <TableCell>
                            <div className="flex items-center justify-between">
                              <div>
                                <div className="font-medium">{match.invoice?.vendor?.name}</div>
                                <div className="text-sm text-muted-foreground">
                                  {currency.formatMonetary(match.invoice?.amountGross, match.invoice?.currencyCode)}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {match.invoice?.date ? new Date(match.invoice.date).toLocaleDateString() : ""}
                                </div>
                              </div>
                              <NextLink href={`/invoices?invoiceId=${match.invoiceId}`} target="_blank" rel="noopener noreferrer">
                                <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700">
                                  <ExternalLink className="h-4 w-4" />
                                </Button>
                              </NextLink>
                            </div>
                          </TableCell>
                          <TableCell className="max-w-xs">
                            <div className="flex items-center justify-between gap-2">
                              {/* Ensure the description container can shrink and text gets truncated */}
                              <div className="min-w-0">
                                <div className="font-medium truncate">{match.transfer?.description}</div>
                                <div className="text-sm text-muted-foreground">
                                  {currency.formatMonetary(match.transfer?.amount, match.transfer?.currencyCode)}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {match.transfer?.transaction?.executedAt ? new Date(match.transfer.transaction.executedAt).toLocaleDateString() : ""}
                                </div>
                              </div>
                              {match.transfer?.transactionId && (
                                <NextLink href={`/transactions?transactionId=${match.transfer.transactionId}`} target="_blank" rel="noopener noreferrer">
                                  <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700">
                                    <ExternalLink className="h-4 w-4" />
                                  </Button>
                                </NextLink>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              {/* Vendor Match */}
                              {(() => {
                                if (!match.detailedScore) return null;
                                const vendorPercentage = getVendorMatchingPercentage(match.detailedScore);
                                return (
                                  <div className="text-xs">
                                    <span className="text-muted-foreground">Vendor: </span>
                                    <span className="font-medium text-purple-600">{vendorPercentage.toFixed(0)}%</span>
                                  </div>
                                );
                              })()}
                              {/* Amount Difference */}
                              {(() => {
                                if (!match.invoice?.amountGross || !match.transfer?.amount) return null;
                                const invoiceAmount = Number(match.invoice.amountGross);
                                const transferAmount = Number(match.transfer.amount);
                                const difference = calculateAmountDifference(invoiceAmount, transferAmount);
                                return (
                                  <div className="text-xs">
                                    <span className="text-muted-foreground">Amount: </span>
                                    <span className={difference === 0 ? "text-green-600 font-medium" : "text-orange-600 font-medium"}>
                                      {difference === 0 ? "Perfect" : currency.formatMonetary(difference, match.transfer?.currencyCode)}
                                    </span>
                                  </div>
                                );
                              })()}

                              {/* Date Difference */}
                              {(() => {
                                if (!match.invoice?.date || !match.transfer?.transaction?.executedAt) return null;
                                const daysDiff = calculateDateDifference(new Date(match.invoice.date), new Date(match.transfer.transaction.executedAt));
                                return (
                                  <div className="text-xs">
                                    <span className="text-muted-foreground">Date: </span>
                                    <span className={daysDiff === 0 ? "text-green-600 font-medium" : "text-blue-600 font-medium"}>
                                      {daysDiff === 0 ? "Same day" : `${daysDiff} day${daysDiff > 1 ? "s" : ""}`}
                                    </span>
                                  </div>
                                );
                              })()}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">{match.transfer?.transaction?.account?.name}</div>
                          </TableCell>
                          <TableCell>{getConfidenceBadge(match.confidenceScore.toFixed(2))}</TableCell>
                          <TableCell>
                            <div className="flex flex-col gap-1">
                              {match.matchReason.split(",").map((reason) => (
                                <Badge variant="outline" className="bg-muted text-muted-foreground">
                                  {reason}
                                </Badge>
                              ))}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Button
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleManualMatch(match.invoiceId, match.transferId);
                              }}
                              disabled={manualMatchMutation.isPending}
                              className="flex items-center gap-1"
                            >
                              <Link className="h-3 w-3" />
                              Match
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">No potential matches found</div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="unmatched" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Unmatched Invoices</CardTitle>
                </CardHeader>
                <CardContent>
                  {unmatchedInvoices.isLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <RefreshCw className="h-6 w-6 animate-spin" />
                    </div>
                  ) : unmatchedInvoices.data && unmatchedInvoices.data.items.length > 0 ? (
                    <div className="space-y-2">
                      {unmatchedInvoices.data.items.map((invoice) => (
                        <div key={invoice.id} className="flex items-center justify-between p-2 border rounded">
                          <div>
                            <div className="font-medium">{invoice.vendor?.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {currency.formatMonetary(invoice.amountGross, invoice.currencyCode)} • {new Date(invoice.date).toLocaleDateString()}
                            </div>
                          </div>
                          <NextLink href={`/invoices?invoiceId=${invoice.id}`} target="_blank" rel="noopener noreferrer">
                            <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700">
                              <ExternalLink className="h-4 w-4" />
                            </Button>
                          </NextLink>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">No unmatched invoices</div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Unmatched Transfers</CardTitle>
                </CardHeader>
                <CardContent>
                  {unmatchedTransfers.isLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <RefreshCw className="h-6 w-6 animate-spin" />
                    </div>
                  ) : unmatchedTransfers.data && unmatchedTransfers.data.items.length > 0 ? (
                    <div className="space-y-2">
                      {unmatchedTransfers.data.items.map((transfer) => (
                        <div key={transfer.id} className="flex items-center justify-between p-2 border rounded">
                          <div>
                            <div className="font-medium">{transfer.transaction?.account?.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {currency.formatMonetary(transfer.amount, transfer.currencyCode)} •{" "}
                              {transfer.transaction?.executedAt ? new Date(transfer.transaction.executedAt).toLocaleDateString() : ""}
                            </div>
                          </div>
                          {transfer.transactionId && (
                            <NextLink href={`/transactions?transactionId=${transfer.transactionId}`} target="_blank" rel="noopener noreferrer">
                              <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700">
                                <ExternalLink className="h-4 w-4" />
                              </Button>
                            </NextLink>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">No unmatched transfers</div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="matched" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Matched Items</CardTitle>
                <CardDescription>All reconciled invoice-transfer pairs</CardDescription>
              </CardHeader>
              <CardContent>
                {reconciliations.isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="h-6 w-6 animate-spin" />
                  </div>
                ) : reconciliations.data && reconciliations.data.reconciliations.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Invoice</TableHead>
                        <TableHead>Transfer</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Confidence</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {reconciliations.data.reconciliations.map((reconciliation) => (
                        <TableRow key={reconciliation.id}>
                          <TableCell>
                            <div className="flex items-center justify-between">
                              <div>
                                <div className="font-medium">{reconciliation.invoice.vendor?.name}</div>
                                <div className="text-sm text-muted-foreground">
                                  {currency.formatMonetary(reconciliation.invoice.amountGross, reconciliation.invoice.currencyCode)}
                                </div>
                              </div>
                              <NextLink href={`/invoices?invoiceId=${reconciliation.invoiceId}`} target="_blank" rel="noopener noreferrer">
                                <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700">
                                  <ExternalLink className="h-4 w-4" />
                                </Button>
                              </NextLink>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center justify-between">
                              <div>
                                <div className="font-medium">{reconciliation.transfer.transaction?.account?.name}</div>
                                <div className="text-sm text-muted-foreground">
                                  {currency.formatMonetary(reconciliation.transfer.amount, reconciliation.transfer.currencyCode)}
                                </div>
                              </div>
                              {reconciliation.transfer.transactionId && (
                                <NextLink
                                  href={`/transactions?transactionId=${reconciliation.transfer.transactionId}`}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                >
                                  <Button variant="ghost" size="sm" className="text-blue-600 hover:text-blue-700">
                                    <ExternalLink className="h-4 w-4" />
                                  </Button>
                                </NextLink>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>{getStatusBadge(reconciliation.status)}</TableCell>
                          <TableCell>{reconciliation.confidenceScore ? getConfidenceBadge(reconciliation.confidenceScore) : "-"}</TableCell>
                          <TableCell>
                            {(reconciliation.status === ReconciliationStatus.AUTO_MATCHED ||
                              reconciliation.status === ReconciliationStatus.MANUALLY_MATCHED) && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleManualUnmatch(reconciliation.invoiceId, reconciliation.transferId)}
                                disabled={manualUnmatchMutation.isPending}
                                className="flex items-center gap-1"
                              >
                                <Unlink className="h-3 w-3" />
                                Unmatch
                              </Button>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">No matched items found</div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Split Drawer for Reconciliation Match Details */}
      <ReconciliationSplitDrawer
        invoiceId={selectedInvoiceId}
        transferId={selectedTransferId}
        open={splitDrawerOpen}
        onOpenChange={setSplitDrawerOpen}
        onNavigateNext={handleNavigateNext}
        onNavigatePrevious={handleNavigatePrevious}
        hasNext={potentialMatches.data ? currentMatchIndex < potentialMatches.data.length - 1 : false}
        hasPrevious={currentMatchIndex > 0}
      />
    </DashboardLayout>
  );
}
