import { trpc } from "@/utils/trpc";
import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHeader, TableRow } from "@/components/ui/table";
import { ChevronLeft, ChevronRight, ExternalLink, Trash2 } from "lucide-react";
import { InvoiceDetailsDrawer } from "@/components/InvoiceDetailsDrawer";
import { useInvoicePdfSetting } from "@/hooks/useInvoicePdfSetting";
import { currency } from "@/modules/core/currency";
import { CurrencyMultiSelect } from "@/components/CurrencyMultiSelect";
import { YearSelect } from "@/components/YearSelect";
import { SearchInput } from "@/components/SearchInput";

import { ReconciliationStatus } from "@/prisma/generated";
import { useRouter } from "next/router";
import { DateRange, DateRangePicker } from "@/components/ui/date-range-picker";
import { AmountRange, AmountRangeFilter } from "@/components/ui/amount-range-filter";
import { VendorFilter } from "@/components/ui/account-filter";
import { DashboardLayout } from "@/components/DashboardLayout";
import { InvoiceTableHeader, SortConfig } from "@/components/InvoiceTableHeader";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";

export default function InvoicesPage() {
  const [limit, setLimit] = useState(20);
  const [cursor, setCursor] = useState<string | undefined>(undefined);
  const [cursors, setCursors] = useState<string[]>([]);
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<string | null>(null);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [selectedCurrencies, setSelectedCurrencies] = useState<string[]>([]);
  const [selectedYear, setSelectedYear] = useState<number | undefined>(undefined);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [selectedReconciliationStatus, setSelectedReconciliationStatus] = useState<ReconciliationStatus | undefined>(undefined);
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [amountRange, setAmountRange] = useState<AmountRange | undefined>(undefined);
  const [selectedVendorId, setSelectedVendorId] = useState<string | undefined>(undefined);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [invoiceToDelete, setInvoiceToDelete] = useState<string | null>(null);
  const [bulkDeleteDialogOpen, setBulkDeleteDialogOpen] = useState(false);
  const [sortConfig, setSortConfig] = useState<SortConfig>({ field: "date", direction: "desc" });

  // PDF display setting
  const { showPdfAlongside, setShowPdfAlongside } = useInvoicePdfSetting();

  const utils = trpc.useUtils();

  // Fetch available currencies
  const currencies = trpc.invoices.getCurrencies.useQuery();

  // Fetch available years
  const availableYears = trpc.invoices.getAvailableYears.useQuery();

  // Fetch available vendors
  const vendors = trpc.vendors.getAll.useQuery();

  // Fetch invoice stats (overall stats without filters)
  const stats = trpc.invoices.getStats.useQuery();

  // Fetch filtered stats when filters are applied
  const hasFilters =
    selectedCurrencies.length > 0 ||
    !!selectedYear ||
    !!searchTerm.trim() ||
    !!selectedReconciliationStatus ||
    !!dateRange?.from ||
    !!dateRange?.to ||
    !!amountRange?.min ||
    !!amountRange?.max ||
    !!selectedVendorId;
  const filteredStats = trpc.invoices.getFilteredStats.useQuery(
    {
      ...(selectedCurrencies.length > 0 ? { currencyCodes: selectedCurrencies } : {}),
      ...(selectedYear && { year: selectedYear }),
      ...(searchTerm.trim() && { search: searchTerm.trim() }),
      ...(selectedReconciliationStatus && { reconciliationStatus: selectedReconciliationStatus }),
      ...(dateRange?.from && { dateFrom: dateRange.from.toISOString() }),
      ...(dateRange?.to && { dateTo: dateRange.to.toISOString() }),
      ...(amountRange?.min !== undefined && { amountMin: amountRange.min }),
      ...(amountRange?.max !== undefined && { amountMax: amountRange.max }),
      ...(selectedVendorId && { vendorId: selectedVendorId }),
    },
    {
      enabled: hasFilters, // Only fetch when filters are applied
    }
  );

  // Fetch invoices with pagination
  const invoices = trpc.invoices.getAll.useQuery({
    limit,
    ...(cursor && { cursor }),
    ...(selectedCurrencies.length > 0 ? { currencyCodes: selectedCurrencies } : {}),
    ...(selectedYear && { year: selectedYear }),
    ...(searchTerm.trim() && { search: searchTerm.trim() }),
    ...(selectedReconciliationStatus && { reconciliationStatus: selectedReconciliationStatus }),
    ...(dateRange?.from && { dateFrom: dateRange.from.toISOString() }),
    ...(dateRange?.to && { dateTo: dateRange.to.toISOString() }),
    ...(amountRange?.min !== undefined && { amountMin: amountRange.min }),
    ...(amountRange?.max !== undefined && { amountMax: amountRange.max }),
    ...(selectedVendorId && { vendorId: selectedVendorId }),
    ...(sortConfig.field &&
      sortConfig.direction && {
        sortField: sortConfig.field as "date" | "vendor" | "amount",
        sortDirection: sortConfig.direction,
      }),
  });

  // Delete mutation
  const deleteMutation = trpc.invoices.delete.useMutation({
    onSuccess: () => {
      toast.success("Invoice deleted successfully");
      utils.invoices.getAll.invalidate();
      utils.invoices.getStats.invalidate();
      utils.invoices.getFilteredStats.invalidate();
      setDeleteDialogOpen(false);
      setInvoiceToDelete(null);
      // Close drawer if the deleted invoice was open
      if (selectedInvoiceId === invoiceToDelete) {
        setDrawerOpen(false);
        setSelectedInvoiceId(null);
      }
    },
    onError: (error) => {
      toast.error(`Failed to delete invoice: ${error.message}`);
    },
  });

  // Bulk delete mutation
  const bulkDeleteMutation = trpc.invoices.bulkDelete.useMutation({
    onSuccess: (result) => {
      toast.success(`Successfully deleted ${result.deletedCount} invoice${result.deletedCount === 1 ? "" : "s"}`);
      utils.invoices.getAll.invalidate();
      utils.invoices.getStats.invalidate();
      utils.invoices.getFilteredStats.invalidate();
      setBulkDeleteDialogOpen(false);
      // Close drawer if it's open
      setDrawerOpen(false);
      setSelectedInvoiceId(null);
    },
    onError: (error) => {
      toast.error(`Failed to delete invoices: ${error.message}`);
    },
  });

  // Router for URL query handling
  const router = useRouter();

  // Handle invoiceId and year from URL query parameters
  useEffect(() => {
    if (router.query.invoiceId && typeof router.query.invoiceId === "string") {
      setSelectedInvoiceId(router.query.invoiceId);
      setDrawerOpen(true);
    }

    if (router.query.year && typeof router.query.year === "string") {
      const year = parseInt(router.query.year, 10);
      if (!isNaN(year)) {
        setSelectedYear(year);
      }
    }
  }, [router.query.invoiceId, router.query.year]);

  const handleNextPage = () => {
    console.log("handleNextPage", invoices.data?.nextCursor);
    if (invoices.data?.nextCursor) {
      setCursors((prev) => [...prev, cursor || ""]);
      setCursor(invoices.data.nextCursor);
    }
  };

  const handlePrevPage = () => {
    if (cursors.length > 0) {
      const newCursors = [...cursors];
      const prevCursor = newCursors.pop();
      setCursors(newCursors);
      setCursor(prevCursor === "" ? undefined : prevCursor);
    }
  };

  const handleLimitChange = (newLimit: string) => {
    setLimit(Number(newLimit));
    setCursor(undefined);
    setCursors([]);
  };

  const handleYearChange = (year: number | undefined) => {
    console.log("handleYearChange", year);
    setSelectedYear(year);
    setCursor(undefined);
    setCursors([]);

    // Update URL with year parameter
    const newQuery = { ...router.query };
    if (year) {
      newQuery.year = year.toString();
    } else {
      delete newQuery.year;
    }

    router.push(
      {
        pathname: router.pathname,
        query: newQuery,
      },
      undefined,
      { shallow: true }
    );
  };

  const handleSearchChange = (search: string) => {
    console.log("handleSearchChange", search);
    setSearchTerm(search);
    setCursor(undefined);
    setCursors([]);
  };

  const handleDeleteInvoice = (invoiceId: string) => {
    setInvoiceToDelete(invoiceId);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (invoiceToDelete) {
      deleteMutation.mutate({ id: invoiceToDelete });
    }
  };

  const handleBulkDelete = () => {
    setBulkDeleteDialogOpen(true);
  };

  const confirmBulkDelete = () => {
    bulkDeleteMutation.mutate({
      ...(selectedCurrencies.length > 0 ? { currencyCodes: selectedCurrencies } : {}),
      ...(selectedYear && { year: selectedYear }),
      ...(searchTerm.trim() && { search: searchTerm.trim() }),
      ...(selectedReconciliationStatus && { reconciliationStatus: selectedReconciliationStatus }),
      ...(dateRange?.from && { dateFrom: dateRange.from.toISOString() }),
      ...(dateRange?.to && { dateTo: dateRange.to.toISOString() }),
      ...(amountRange?.min !== undefined && { amountMin: amountRange.min }),
      ...(amountRange?.max !== undefined && { amountMax: amountRange.max }),
      ...(selectedVendorId && { vendorId: selectedVendorId }),
    });
  };

  const handleInvoiceClick = (invoiceId: string) => {
    setSelectedInvoiceId(invoiceId);
    setDrawerOpen(true);
    // Update the URL with the selected invoiceId for persistence
    router.push(
      {
        pathname: router.pathname,
        query: { ...router.query, invoiceId },
      },
      undefined,
      { shallow: true }
    );
  };

  const handleDrawerOpenChange = (open: boolean) => {
    setDrawerOpen(open);
    if (!open) {
      setSelectedInvoiceId(null);

      // Remove invoiceId from the URL when the drawer is closed
      const { invoiceId: _removed, ...remainingQuery } = router.query as Record<string, any>;
      router.push(
        {
          pathname: router.pathname,
          query: remainingQuery,
        },
        undefined,
        { shallow: true }
      );
    }
  };

  const handleNavigateNext = () => {
    if (!invoices.data?.items || !selectedInvoiceId) return;

    const currentIndex = invoices.data.items.findIndex((invoice) => invoice.id === selectedInvoiceId);
    if (currentIndex < invoices.data.items.length - 1) {
      const nextInvoice = invoices.data.items[currentIndex + 1];
      setSelectedInvoiceId(nextInvoice.id);
    }
  };

  const handleNavigatePrevious = () => {
    if (!invoices.data?.items || !selectedInvoiceId) return;

    const currentIndex = invoices.data.items.findIndex((invoice) => invoice.id === selectedInvoiceId);
    if (currentIndex > 0) {
      const previousInvoice = invoices.data.items[currentIndex - 1];
      setSelectedInvoiceId(previousInvoice.id);
    }
  };

  const getNavigationState = () => {
    if (!invoices.data?.items || !selectedInvoiceId) {
      return { hasNext: false, hasPrevious: false };
    }

    const currentIndex = invoices.data.items.findIndex((invoice) => invoice.id === selectedInvoiceId);
    return {
      hasNext: currentIndex < invoices.data.items.length - 1,
      hasPrevious: currentIndex > 0,
    };
  };

  const { hasNext, hasPrevious } = getNavigationState();

  // Reset pagination when any filter changes
  useEffect(() => {
    setCursor(undefined);
    setCursors([]);
  }, [selectedCurrencies, selectedYear, searchTerm, selectedReconciliationStatus, dateRange, amountRange, selectedVendorId, sortConfig]);

  // Keep the URL in sync when navigating between invoices in the drawer
  useEffect(() => {
    if (!drawerOpen || !selectedInvoiceId) return;

    router.push(
      {
        pathname: router.pathname,
        query: { ...router.query, invoiceId: selectedInvoiceId },
      },
      undefined,
      { shallow: true }
    );
  }, [selectedInvoiceId, drawerOpen]);

  return (
    <DashboardLayout title="Invoices">
      <div className="container mx-auto space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Invoices</h1>
            <p className="text-muted-foreground">View and manage your processed invoices</p>
          </div>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {stats.data ? (
            <>
              <Card className="py-3 gap-0">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-600">{currency.formatMonetary(stats.data.totalAmountGross, "EUR")}</div>
                </CardContent>
              </Card>
              <Card className="py-3 gap-0">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Vat</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-600">{currency.formatMonetary(stats.data.totalAmountVat, "EUR")}</div>
                </CardContent>
              </Card>
              <Card className="py-3 gap-0">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Invoice</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.data.total}</div>
                </CardContent>
              </Card>
              {/* <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">With VAT</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{stats.data.withVAT}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Without VAT</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{stats.data.withoutVAT}</div>
              </CardContent>
            </Card> */}
            </>
          ) : (
            <div className="col-span-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center text-muted-foreground">{stats.isLoading ? "Loading stats..." : "Failed to load stats"}</div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        {/* Filters Section */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {/* Search Input */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Search</label>
                <SearchInput value={searchTerm} onChange={handleSearchChange} placeholder="Search invoices and vendors..." />
              </div>

              {/* Year Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Year</label>
                <YearSelect years={availableYears.data || []} selectedYear={selectedYear} onChange={handleYearChange} />
              </div>

              {/* Currency Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Currency</label>
                <CurrencyMultiSelect currencies={currencies.data || []} selected={selectedCurrencies} onChange={setSelectedCurrencies} />
              </div>

              {/* Date Range Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Date Range</label>
                <DateRangePicker dateRange={dateRange} onDateRangeChange={setDateRange} placeholder="Filter by date" />
              </div>

              {/* Amount Range Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Amount Range</label>
                <AmountRangeFilter amountRange={amountRange} onAmountRangeChange={setAmountRange} placeholder="Filter by amount" currency="€" />
              </div>

              {/* Vendor Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Vendor</label>
                <VendorFilter
                  vendors={vendors.data?.items?.map((v) => ({ id: v.id, name: v.name })) || []}
                  selectedVendorId={selectedVendorId}
                  onVendorChange={setSelectedVendorId}
                  placeholder="All vendors"
                />
              </div>

              {/* Reconciliation Status Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Reconciliation Status</label>
                <Select
                  value={selectedReconciliationStatus || "all"}
                  onValueChange={(value) => setSelectedReconciliationStatus(value === "all" ? undefined : (value as ReconciliationStatus))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All statuses</SelectItem>
                    <SelectItem value="MATCHED">Matched</SelectItem>
                    <SelectItem value="UNMATCHED">Unmatched</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Bulk Delete Button - only show when filters are active */}
              {hasFilters && (
                <div className="space-y-2">
                  <label className="text-sm font-medium">Actions</label>
                  <Button
                    variant="destructive"
                    onClick={handleBulkDelete}
                    disabled={bulkDeleteMutation.isPending || !filteredStats.data?.total}
                    className="w-full"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    {bulkDeleteMutation.isPending ? "Deleting..." : `Delete ${filteredStats.data?.total || 0} Filtered`}
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Filtered Stats Section */}
        {hasFilters && (filteredStats.data?.total ?? 0) > 0 && (
          <Card className="py-2 gap-0">
            <CardHeader>
              <CardTitle className="text-md text-muted-foreground">Statistics for the current filters</CardTitle>
            </CardHeader>
            <CardContent>
              {filteredStats.isLoading ? (
                <div className="text-center text-muted-foreground">Loading filtered stats...</div>
              ) : filteredStats.data ? (
                <div className="flex gap-6">
                  <div className="flex items-center gap-2">
                    <div className="text-sm text-muted-foreground">Invoice Count</div>
                    <div className="text-lg font-semibold">{filteredStats.data.total}</div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="text-sm text-muted-foreground">Total Amount</div>
                    <div className="text-lg font-semibold">{currency.formatMonetary(filteredStats.data.totalAmountGross, "EUR")}</div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="text-sm text-muted-foreground">VAT</div>
                    <div className="text-lg font-semibold">{currency.formatMonetary(filteredStats.data.totalAmountVat, "EUR")}</div>
                  </div>
                </div>
              ) : (
                <div className="text-center text-destructive">Failed to load filtered stats</div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Controls */}
        <div className="flex items-center justify-end">
          {/* Pagination Controls */}
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Rows per page:</span>
              <Select value={limit.toString()} onValueChange={handleLimitChange}>
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {[10, 20, 50, 100].map((value) => (
                    <SelectItem key={value} value={value.toString()}>
                      {value}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Button variant="outline" size="sm" onClick={handlePrevPage} disabled={cursors.length === 0}>
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <Button variant="outline" size="sm" onClick={handleNextPage} disabled={!invoices.data?.nextCursor}>
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Invoices Data Table */}
        <Card>
          {/* <CardHeader>
            <CardTitle>Invoices</CardTitle>
            <CardDescription>A list of all processed invoices with their details</CardDescription>
          </CardHeader> */}
          <CardContent>
            {invoices.isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-muted-foreground">Loading invoices...</div>
              </div>
            ) : invoices.data ? (
              <>
                {invoices.data.items.length === 0 ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="text-muted-foreground">No invoices found</div>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <InvoiceTableHeader sortConfig={sortConfig} onSortChange={setSortConfig} />
                    </TableHeader>
                    <TableBody>
                      {invoices.data.items.map((invoice) => (
                        <TableRow key={invoice.id} className="cursor-pointer hover:bg-muted/50" onClick={() => handleInvoiceClick(invoice.id)}>
                          <TableCell className="text-sm text-muted-foreground">{invoice.date.toLocaleDateString("de-DE")}</TableCell>
                          <TableCell className="max-w-xs">
                            <div className="truncate text-sm">{invoice.vendor?.name || "Unknown Vendor"}</div>
                          </TableCell>
                          <TableCell className="text-sm">
                            <div className="text-sm">
                              <div className="font-medium">{currency.formatMonetary(invoice.amountGross, "EUR")}</div>
                              {(invoice.amountVat as any) > 0 && (
                                <div className="text-xs text-muted-foreground">Net: {currency.formatMonetary(invoice.amountNet, "EUR")}</div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-sm">
                            <div className="font-medium">{currency.formatMonetary(invoice.sourceAmountGross, invoice.currencyCode)}</div>
                          </TableCell>
                          <TableCell className="text-sm">
                            <div className="font-medium">{currency.formatMonetary(invoice.amountVat, "EUR")}</div>
                          </TableCell>
                          {/* <TableCell>
                          <div className="flex items-center gap-2">
                            <Badge variant={invoice.hasVAT ? "default" : "secondary"}>{invoice.hasVAT ? "With VAT" : "No VAT"}</Badge>
                            {invoice.isReverseCharge && <Badge variant="outline">Reverse Charge</Badge>}
                          </div>
                        </TableCell> */}
                          <TableCell>
                            <Badge variant="outline">{invoice.importItem?.importItemType}</Badge>
                          </TableCell>
                          <TableCell className="font-mono text-sm">{invoice.invoiceReference}</TableCell>
                          <TableCell>
                            {invoice.reconciliations && invoice.reconciliations.length > 0 ? (
                              <div className="flex items-center gap-1">
                                <Badge variant="default" className="bg-green-100 text-green-800">
                                  Matched
                                </Badge>
                                {invoice.reconciliations[0].confidenceScore && (
                                  <span className="text-xs text-muted-foreground">{invoice.reconciliations[0].confidenceScore}%</span>
                                )}
                              </div>
                            ) : (
                              <Badge variant="secondary">Unmatched</Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-right" onClick={(e) => e.stopPropagation()}>
                            <div className="flex items-center justify-end gap-2">
                              {invoice.importItem.fileUrl && (
                                <Button variant="outline" size="sm" asChild>
                                  <a href={invoice.importItem.fileUrl} target="_blank" rel="noopener noreferrer">
                                    <ExternalLink className="h-4 w-4" />
                                    View File
                                  </a>
                                </Button>
                              )}
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDeleteInvoice(invoice.id)}
                                className="text-destructive hover:text-destructive cursor-pointer"
                              >
                                <Trash2 className="h-4 w-4" />
                                Delete
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </>
            ) : (
              <div className="flex items-center justify-center py-8">
                <div className="text-destructive">Failed to load invoices: {invoices.error?.message}</div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Invoice Details Drawer */}
        <InvoiceDetailsDrawer
          invoiceId={selectedInvoiceId}
          open={drawerOpen}
          onOpenChange={handleDrawerOpenChange}
          onNavigateNext={handleNavigateNext}
          onNavigatePrevious={handleNavigatePrevious}
          hasNext={hasNext}
          hasPrevious={hasPrevious}
          showPdfAlongside={showPdfAlongside}
          onTogglePdfAlongside={setShowPdfAlongside}
        />

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the invoice and all associated data including line items and reconciliations.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmDelete}
                disabled={deleteMutation.isPending}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {deleteMutation.isPending ? "Deleting..." : "Delete"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Bulk Delete Confirmation Dialog */}
        <AlertDialog open={bulkDeleteDialogOpen} onOpenChange={setBulkDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Filtered Invoices</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete all invoices matching the current filters and all their associated data including
                line items and reconciliations.
                {filteredStats.data && (
                  <div className="mt-2 p-2 bg-muted rounded">
                    <strong>
                      This will delete {filteredStats.data.total} invoice{filteredStats.data.total === 1 ? "" : "s"}
                    </strong>
                  </div>
                )}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmBulkDelete}
                disabled={bulkDeleteMutation.isPending}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {bulkDeleteMutation.isPending ? "Deleting..." : "Delete All Filtered"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </DashboardLayout>
  );
}
