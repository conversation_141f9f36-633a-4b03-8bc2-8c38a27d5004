import { z } from "zod";
import { createTR<PERSON><PERSON>outer, publicProcedure } from "@/server/trpc";
import { TRPCError } from "@trpc/server";
import { csvUtils } from "@/modules/core/utils/csvUtils";
import { binanceUtils } from "@/modules/core/utils/binanceUtils";
import { s3 } from "@/modules/core/s3";
import { MotlFile } from "@/modules/core/motlFile";
import { json } from "../core/utils/jsonUtils";
import { v4 as uuid } from "uuid";

// Input validation schemas
const uploadCSVSchema = z.object({
  fileContent: z.string().min(1, "File content is required"),
  fileName: z.string().min(1, "File name is required"),
  csvType: z.enum(["binance-card", "generic"]).default("generic"),
});

const processCSVSchema = z
  .object({
    fileContent: z.string().min(1, "File content is required"),
    csvType: z.enum(["binance-card", "generic"]),
    accountId: z.string(),
    createNewAccount: z.boolean().default(false),
    newAccountName: z.string().optional(),
    newAccountType: z.enum(["WALLET", "BANK_ACCOUNT", "EXCHANGE_ACCOUNT", "CREDIT_CARD"]).optional(),
  })
  .refine(
    (data) => {
      // If not creating new account, accountId must be provided
      if (!data.createNewAccount) {
        return data.accountId.length > 0;
      }
      // If creating new account, newAccountName and newAccountType must be provided
      return data.newAccountName && data.newAccountName.length > 0 && data.newAccountType;
    },
    {
      message: "Either select an existing account or provide new account details",
      path: ["accountId"],
    }
  );

const previewCSVSchema = z.object({
  fileContent: z.string().min(1, "File content is required"),
  csvType: z.enum(["binance-card", "generic"]),
  accountName: z.string().min(1, "Account name is required"),
});

export const csvRouter = createTRPCRouter({
  /**
   * Upload and analyze CSV file structure
   */
  uploadAndAnalyze: publicProcedure.input(uploadCSVSchema).mutation(async ({ input, ctx }) => {
    try {
      // Parse the CSV content
      const parseResult = csvUtils.parseCSV(input.fileContent);
      if (parseResult.isErr()) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `CSV parsing failed: ${parseResult.error.message}`,
          cause: parseResult.error,
        });
      }

      const parsed = parseResult.value;
      const analysis = csvUtils.analyzeCSVStructure(parsed);

      // Upload file to S3 for later processing
      const motlFile = await MotlFile.parse(Buffer.from(input.fileContent), input.fileName);
      const uploadResult = await s3.upload({ file: motlFile, public: false });

      return {
        fileUrl: uploadResult.url,
        analysis,
        rowCount: parsed.rowCount,
        headers: parsed.headers,
        preview: parsed.preview,
        detectedType: detectCSVType(parsed.headers),
      };
    } catch (error) {
      console.error("Failed to upload and analyze CSV:", error);
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to upload and analyze CSV",
        cause: error,
      });
    }
  }),

  /**
   * Preview transactions that would be created from CSV
   */
  previewTransactions: publicProcedure.input(previewCSVSchema).mutation(async ({ input, ctx }) => {
    try {
      if (input.csvType === "binance-card") {
        // Parse Binance CSV
        const parseResult = csvUtils.parseBinanceCSV(input.fileContent);
        if (parseResult.isErr()) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Binance CSV parsing failed: ${parseResult.error.message}`,
            cause: parseResult.error,
          });
        }

        // Process transactions
        const processResult = binanceUtils.processBinanceTransactions(parseResult.value, input.accountName);
        if (processResult.isErr()) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Transaction processing failed: ${processResult.error.message}`,
            cause: processResult.error,
          });
        }

        const processed = processResult.value;

        return {
          totalTransactions: processed.length,
          totalTransfers: processed.length,
          totalTrades: processed.reduce((sum, t) => sum + t.trades.length, 0),
          preview: processed.slice(0, 5).map((t) => ({
            transfer: {
              ...t.transfer,
              amount: t.transfer.amount.toString(),
            },
            trades: t.trades.map((trade) => ({
              ...trade,
              amountFrom: trade.amountFrom.toString(),
              amountTo: trade.amountTo.toString(),
            })),
          })),
          summary: {
            dateRange: {
              from: Math.min(...parseResult.value.map((t) => t.timestamp.getTime())),
              to: Math.max(...parseResult.value.map((t) => t.timestamp.getTime())),
            },
            currencies: [...new Set(processed.flatMap((t) => [t.transfer.currencyCode, ...t.trades.flatMap((trade) => [trade.tokenFrom, trade.tokenTo])]))],
            totalAmount: processed.reduce((sum, t) => sum + parseFloat(t.transfer.amount.toString()), 0),
          },
        };
      } else {
        // Generic CSV processing - just return structure analysis
        const parseResult = csvUtils.parseCSV(input.fileContent);
        if (parseResult.isErr()) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `CSV parsing failed: ${parseResult.error.message}`,
            cause: parseResult.error,
          });
        }

        return {
          totalTransactions: parseResult.value.rowCount,
          totalTransfers: 0,
          totalTrades: 0,
          preview: [],
          summary: {
            dateRange: { from: 0, to: 0 },
            currencies: [],
            totalAmount: 0,
          },
          message: "Generic CSV preview not yet implemented. Please use Binance card format.",
        };
      }
    } catch (error) {
      console.error("Failed to preview transactions:", error);
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to preview transactions",
        cause: error,
      });
    }
  }),

  /**
   * Process CSV and create transactions in database
   */
  processAndImport: publicProcedure.input(processCSVSchema).mutation(async ({ input, ctx }) => {
    try {
      let accountId = input.accountId;
      let account;

      // Create new account if requested
      if (input.createNewAccount) {
        if (!input.newAccountName || !input.newAccountType) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "New account name and type are required when creating a new account",
          });
        }

        account = await ctx.prisma.account.create({
          data: {
            name: input.newAccountName,
            type: input.newAccountType,
          },
        });
        accountId = account.id;
      } else {
        // Verify existing account exists
        account = await ctx.prisma.account.findUnique({
          where: { id: accountId },
        });

        if (!account) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Account not found",
          });
        }
      }

      if (input.csvType === "binance-card") {
        // Parse and process Binance CSV
        const parseResult = csvUtils.parseBinanceCSV(input.fileContent);
        if (parseResult.isErr()) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Binance CSV parsing failed: ${parseResult.error.message}`,
            cause: parseResult.error,
          });
        }

        const processResult = binanceUtils.processBinanceTransactions(parseResult.value, account.name);
        if (processResult.isErr()) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Transaction processing failed: ${processResult.error.message}`,
            cause: processResult.error,
          });
        }

        const processed = processResult.value;

        // Create transactions in database using transaction with timeout
        let result;
        try {
          result = await ctx.prisma.$transaction(
            async (prisma) => {
              const createdTransactions = [];

              for (const processedTx of processed) {
                // Generate a group ID for related transactions (transfer + trades)
                const transactionGroupId = crypto.randomUUID();

                // Create transaction for transfer
                const transferTransaction = await prisma.transaction.create({
                  data: {
                    executedAt: processedTx.transfer.executedAt,
                    accountId: accountId,
                    transactionGroupId: transactionGroupId,
                    metadata: {
                      source: "binance-card-csv",
                      originalDescription: processedTx.transfer.description,
                      type: "transfer",
                    },
                  },
                });

                // Create transfer
                const transfer = await prisma.transfer.create({
                  data: {
                    counterparty: processedTx.transfer.counterparty,
                    amount: processedTx.transfer.amount,
                    currencyCode: processedTx.transfer.currencyCode,
                    description: processedTx.transfer.description,
                    transactionId: transferTransaction.id,
                  },
                });

                // Trigger auto-reconciliation for the new transfer
                try {
                  const { autoReconcileForTransfer } = await import("@/modules/reconciliation/reconciliationService");
                  const reconcileResult = await autoReconcileForTransfer(transfer.id);
                  if (reconcileResult.isOk() && reconcileResult.value > 0) {
                    console.log(`Auto-matched ${reconcileResult.value} invoices for new transfer ${transfer.id}`);
                  }
                } catch (reconcileError) {
                  console.error("Auto-reconciliation failed for new transfer:", reconcileError);
                  // Don't fail the import if reconciliation fails
                }

                // Create separate transactions for each trade
                const trades = [];
                for (const tradeData of processedTx.trades) {
                  const tradeTransaction = await prisma.transaction.create({
                    data: {
                      executedAt: tradeData.executedAt,
                      accountId: accountId,
                      transactionGroupId: transactionGroupId,
                      metadata: {
                        source: "binance-card-csv",
                        originalDescription: tradeData.description,
                        type: "trade",
                      },
                    },
                  });

                  const trade = await prisma.trade.create({
                    data: {
                      poolId: tradeData.poolId,
                      description: tradeData.description,
                      tokenFrom: tradeData.tokenFrom,
                      tokenTo: tradeData.tokenTo,
                      amountFrom: tradeData.amountFrom,
                      amountTo: tradeData.amountTo,
                      transactionId: tradeTransaction.id,
                    },
                  });
                  trades.push({ transaction: tradeTransaction, trade });
                }

                createdTransactions.push({
                  transferTransaction,
                  transfer,
                  trades,
                });
              }

              return createdTransactions;
            },
            {
              maxWait: 60000, // 30 seconds
              timeout: 60000, // 60 seconds
            }
          );
        } catch (transactionError) {
          console.error("Database transaction failed:", transactionError);
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to save transactions to database. Please try again.",
            cause: transactionError,
          });
        }

        return {
          success: true,
          accountId,
          accountName: account.name,
          transactionsCreated: result.length + result.reduce((sum, t) => sum + t.trades.length, 0),
          transfersCreated: result.length,
          tradesCreated: result.reduce((sum, t) => sum + t.trades.length, 0),
          summary: {
            totalAmount: processed.reduce((sum, t) => sum + parseFloat(t.transfer.amount.toString()), 0),
            dateRange: {
              from: Math.min(...parseResult.value.map((t) => t.timestamp.getTime())),
              to: Math.max(...parseResult.value.map((t) => t.timestamp.getTime())),
            },
          },
        };
      } else {
        const parseResult = await csvUtils.parseGenericCSV(input.fileContent);
        if (parseResult.isErr()) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `CSV parsing failed: ${json.prettyPrint(parseResult.error as any)}`,
            cause: parseResult.error,
          });
        }

        const operations = parseResult.value.data.map((row) => {
          const id = uuid();
          const transactionOp = ctx.prisma.transaction.create({
            data: {
              id,
              executedAt: row.date,
              accountId: accountId,
            },
          });

          const transferOp = ctx.prisma.transfer.create({
            data: {
              id,
              counterparty: row.counterparty,
              amount: row.amount,
              currencyCode: row.currencyCode!,
              description: row.description,
              transactionId: id,
              externalId: row.externalId,
            },
          });

          return [transactionOp, transferOp];
        }).flat();

        await ctx.prisma.$transaction(operations);
        // await ctx.prisma.$transaction(
        //   async (prisma) => {
        //     const createdTransactions = [];

        //     for (const row of parseResult.value.data as any[]) {
        //       const transaction = await prisma.transaction.create({
        //         data: {
        //           executedAt: row.date,
        //           accountId: accountId,
        //         },
        //       });

        //       const transfer = await prisma.transfer.create({
        //         data: {
        //           counterparty: row.counterparty,
        //           amount: row.amount,
        //           currencyCode: row.currencyCode,
        //           description: row.description,
        //           transactionId: transaction.id,
        //           externalId: row.externalId,
        //         },
        //       });
        //     }
        //   },
        //   {
        //     maxWait: 30000, // 30 seconds
        //     timeout: 60000, // 60 seconds
        //   }
        // );
      }
    } catch (error) {
      console.error("Failed to process and import CSV:", error);
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to process and import CSV",
        cause: error,
      });
    }
  }),
});

/**
 * Detect CSV type based on headers
 */
function detectCSVType(headers: string[]): "binance-card" | "generic" {
  const binanceHeaders = ["Timestamp", "Description", "Paid OUT (EUR)", "Paid IN (EUR)", "Transaction Fee (EUR)", "Assets Used", "Exchange Rates"];
  const hasAllBinanceHeaders = binanceHeaders.every((header) => headers.includes(header));

  if (hasAllBinanceHeaders) {
    return "binance-card";
  }

  return "generic";
}
