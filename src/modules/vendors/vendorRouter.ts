import { z } from "zod";
import { createTRPCRouter, publicProcedure } from "@/server/trpc";
import { TRPCError } from "@trpc/server";

export const vendorRouter = createTRPCRouter({
  getAll: publicProcedure
    .input(
      z
        .object({
          limit: z.number().min(1).max(100).default(10),
          cursor: z.string().optional(),
          search: z.string().optional(),
          sortField: z.enum(["name", "location", "contact", "vatNumber", "invoiceCount", "totalAmount", "invoiceKind"]).optional(),
          sortDirection: z.enum(["asc", "desc"]).optional(),
        })
        .optional()
        .default({})
    )
    .query(async ({ input, ctx }) => {
      try {
        const limit = input?.limit ?? 10;
        const cursor = input?.cursor;

        // Build where clause
        let whereClause: any = {};

        // Search filter
        if (input?.search && input.search.trim()) {
          const searchTerm = input.search.trim();
          whereClause.OR = [
            {
              name: {
                search: searchTerm,
              },
            },
            {
              country: {
                search: searchTerm,
              },
            },
            {
              city: {
                search: searchTerm,
              },
            },
            {
              vatNumber: {
                search: searchTerm,
              },
            },
            {
              email: {
                search: searchTerm,
              },
            },
            {
              contactPerson: {
                search: searchTerm,
              },
            },
            {
              phone: {
                search: searchTerm,
              },
            },
          ];
        }

        // Build orderBy clause
        let orderBy: any = { name: "asc" }; // Default sort
        if (input?.sortField && input?.sortDirection) {
          switch (input.sortField) {
            case "name":
              orderBy = { name: input.sortDirection };
              break;
            case "location":
              orderBy = { city: input.sortDirection };
              break;
            case "contact":
              orderBy = { contactPerson: input.sortDirection };
              break;
            case "vatNumber":
              orderBy = { vatNumber: input.sortDirection };
              break;
            case "invoiceCount":
              orderBy = { invoices: { _count: input.sortDirection } };
              break;
            case "totalAmount":
              orderBy = { name: "asc" }; // Fallback to name for now
              break;
            case "invoiceKind":
              orderBy = { name: "asc" }; // Fallback to name for now
              break;
          }
        }

        const vendors = await ctx.prisma.vendor.findMany({
          take: limit + 1, // Take one extra to check if there's a next page
          cursor: cursor ? { id: cursor } : undefined,
          orderBy,
          where: Object.keys(whereClause).length > 0 ? whereClause : undefined,
          include: {
            invoices: {
              select: {
                id: true,
                amountGross: true,
                date: true,
                importItem: {
                  select: {
                    importItemType: true,
                  },
                },
              },
              orderBy: { date: "desc" },
            },
          },
        });

        // Handle complex sorting that requires post-processing
        if (input?.sortField && input?.sortDirection && ["totalAmount", "invoiceKind"].includes(input.sortField)) {
          vendors.sort((a, b) => {
            let comparison = 0;

            switch (input.sortField) {
              case "totalAmount":
                const totalA = a.invoices.reduce((sum, inv) => sum + Number(inv.amountGross), 0);
                const totalB = b.invoices.reduce((sum, inv) => sum + Number(inv.amountGross), 0);
                comparison = totalA - totalB;
                break;
              case "invoiceKind":
                // Get the most common import type for each vendor
                const getMostCommonType = (invoices: any[]) => {
                  if (invoices.length === 0) return "";
                  const typeCounts: Record<string, number> = {};
                  invoices.forEach((inv) => {
                    const type = inv.importItem?.importItemType || "UNKNOWN";
                    typeCounts[type] = (typeCounts[type] || 0) + 1;
                  });
                  return Object.entries(typeCounts).sort(([, a], [, b]) => b - a)[0]?.[0] || "";
                };
                const typeA = getMostCommonType(a.invoices);
                const typeB = getMostCommonType(b.invoices);
                comparison = typeA.localeCompare(typeB);
                break;
            }

            return input.sortDirection === "desc" ? -comparison : comparison;
          });
        }

        let nextCursor: string | undefined = undefined;
        if (vendors.length > limit) {
          const nextItem = vendors.pop(); // Remove the extra item
          nextCursor = nextItem!.id;
        }

        return {
          items: vendors,
          nextCursor,
        };
      } catch (error) {
        console.error("Failed to fetch vendors:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch vendors",
          cause: error,
        });
      }
    }),

  getById: publicProcedure.input(z.object({ id: z.string() })).query(async ({ input, ctx }) => {
    try {
      const vendor = await ctx.prisma.vendor.findUnique({
        where: { id: input.id },
        include: {
          invoices: {
            include: {
              importItem: {
                select: {
                  id: true,
                  externalId: true,
                  fileUrl: true,
                  createdAt: true,
                  importItemType: true,
                },
              },
            },
            orderBy: { date: "desc" },
          },
        },
      });

      if (!vendor) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Vendor not found",
        });
      }

      return vendor;
    } catch (error) {
      console.error("Failed to fetch vendor:", error);
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch vendor",
        cause: error,
      });
    }
  }),

  getStats: publicProcedure.query(async ({ ctx }) => {
    try {
      const [total, withVAT, withoutVAT, withInvoices] = await Promise.all([
        ctx.prisma.vendor.count(),
        ctx.prisma.vendor.count({
          where: {
            invoices: {
              some: {
                hasVAT: true,
              },
            },
          },
        }),
        ctx.prisma.vendor.count({
          where: {
            invoices: {
              some: {
                hasVAT: false,
              },
            },
          },
        }),
        ctx.prisma.vendor.count({
          where: {
            invoices: {
              some: {},
            },
          },
        }),
      ]);

      return {
        total,
        withVAT,
        withoutVAT,
        withInvoices,
      };
    } catch (error) {
      console.error("Failed to fetch vendor stats:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch vendor stats",
        cause: error,
      });
    }
  }),

  delete: publicProcedure.input(z.object({ id: z.string() })).mutation(async ({ input, ctx }) => {
    try {
      // Check if vendor has any invoices
      const vendorWithInvoices = await ctx.prisma.vendor.findUnique({
        where: { id: input.id },
        include: {
          invoices: {
            select: { id: true },
          },
        },
      });

      if (!vendorWithInvoices) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Vendor not found",
        });
      }

      if (vendorWithInvoices.invoices.length > 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Cannot delete vendor with existing invoices",
        });
      }

      await ctx.prisma.vendor.delete({
        where: { id: input.id },
      });

      return { success: true };
    } catch (error) {
      console.error("Failed to delete vendor:", error);
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to delete vendor",
        cause: error,
      });
    }
  }),
});
