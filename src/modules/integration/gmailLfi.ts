import "dotenv/config";
import { gmail_v1 } from "googleapis";
import fs from "fs";
import PQueue from "p-queue";
import * as R from "remeda";
import * as lfi from "lfi";
import { google } from "./googleClient";
import jetpack from "fs-jetpack";
import { mistral } from "../ai/mistral";
import { s3 } from "../core/s3";
import pdf from "pdf-parse";
import { prisma } from "../../prisma/prisma";
import { MotlFile } from "../core/motlFile";
import * as aq from "arquero";
import yargs from "yargs";
import { pdfPlumberApi } from "../pdfParser/pdfPlumberApi";
import { date } from "../core/utils/dateUtils";
import { measureTime } from "../../utils";
import { documentAI } from "../pdfParser/documentAi";
import limitConcur from "limit-concur";

async function getAllMail() {
  const getMessageQueue = new PQueue({
    interval: 1000,
    intervalCap: 50,
  });

  const getAttachmentQueue = new PQueue({
    interval: 1000,
    intervalCap: 50,
  });

  await measureTime("getAllMail", async () => {
    const userResp = await google.directory.users.list({ customer: "my_customer", maxResults: 500 });

    const users = userResp.data.users ?? [];

    const gmailClients = users.map((user) => {
      const gmail = google.getGmailClient(user.primaryEmail!);
      return {
        gmail,
      };
    });

    const fetchGmailAttachemnts = async (args: { gmailClient: gmail_v1.Gmail; testArgs?: { limit?: number } }) =>
      lfi.pipe(
        lfi.asConcur(
          await (async function* () {
            console.log("in fetch all emails");

            let pageToken: string | undefined = undefined;
            while (true) {
              const searchKeywordQuery = ["rechnung", "invoice", "honorar"].join(" OR ");
              // googleClient.auth.subject = financeUserEmail!.primaryEmail!; // set the user for delegation
              const messageRes: gmail_v1.Schema$ListMessagesResponse = await args.gmailClient.users.messages
                .list({
                  userId: "me",
                  maxResults: args.testArgs?.limit === -1 ? undefined : args.testArgs?.limit ?? 100,
                  pageToken,
                  q: `has:attachment (${searchKeywordQuery})`,
                })

                .then((res) => res.data);

              for (const msg of messageRes.messages ?? []) yield msg;

              pageToken = (messageRes as any).nextPageToken;

              if (args.testArgs?.limit != -1 && messageRes.messages?.length! > args.testArgs?.limit!) break;

              if (!pageToken) break;
            }
          })()
        ),
        lfi.mapConcur(async (msg) => {
          const message = await getMessageQueue.add(async () =>
            args.gmailClient.users.messages.get({
              userId: "me",
              id: msg.id!,
            })
          );
          if (!message) throw new Error("Message not found");

          const bodyParts = message.data.payload?.parts?.find((p) => p.mimeType === "multipart/alternative");

          function getPart(mimeType: string) {
            const part = (bodyParts?.parts ?? []).find((p) => p.mimeType === mimeType);
            return part ? Buffer.from(part.body?.data ?? "", "base64url").toString("utf8") : undefined;
          }

          let bodyPlain = getPart("text/plain");
          let bodyHtml = getPart("text/html");

          const bodyText = bodyHtml ?? bodyPlain ?? "";

          const messageParsed = {
            subject: message.data.payload?.headers?.find((h) => h.name === "Subject")?.value ?? "",
            from: message.data.payload?.headers?.find((h) => h.name === "From")?.value ?? "",
            to: message.data.payload?.headers?.find((h) => h.name === "To")?.value ?? "",
            body: {
              bodyText,
              bodyPlain,
              bodyHtml,
            },
          };

          return { ...message.data, ...messageParsed };
        }),

        lfi.filterMapConcur(async (message) => {
          const revelantAttachments = message.payload?.parts?.filter((part) => ["application/pdf", "application/octet-stream"].includes(part.mimeType!));
          if (!revelantAttachments || revelantAttachments.length === 0) return undefined;

          return revelantAttachments.map((part) => ({
            ...part,
            message,
          }));
        }),
        lfi.flatMapConcur((parts) => parts),
        lfi.mapConcur(async (part) => {
          const attachment = await getAttachmentQueue.add(() =>
            args.gmailClient.users.messages.attachments.get({
              userId: "me",
              messageId: part.message.id!,
              id: part.body!.attachmentId!,
            })
          );
          if (!attachment) throw new Error("Attachment not found");

          const motlFile = await MotlFile.parse(attachment.data.data!);

          const uploadRes = await s3.upload({
            file: motlFile,
          });

          const filePath = `${dataPath}/attachments/${part.filename}`;

          // create public url for the attachment
          jetpack.write(filePath, motlFile.buffer);

          return {
            ...attachment.data,
            attachmentId: part.body!.attachmentId!,
            motlFile,
            fileUrl: uploadRes.url,
            buffer: motlFile.buffer,
            ...part,
            message: part.message,
          };
        }),

        lfi.mapConcur(async (attachment) => {
          try {
            if (attachment.mimeType === "application/pdf") {
              const pagesCount = await pdf(attachment.buffer).then((res) => res.numpages);

              let parsedTextRes = await documentAI.parseDocument({ file: attachment.motlFile });

              if (parsedTextRes.isErr()) {
                console.error(`error parsing ${attachment.filename}, `, parsedTextRes.error);
                return { ...attachment, type: "error", error: parsedTextRes.error } as const;
              }

              let parsedText = parsedTextRes.value.parsedText;

              return { ...attachment, fileType: "pdf", parsedText, pagesCount, type: "success" } as const;
            }
            return { ...attachment, fileType: "nonPdf", type: "noPdf" } as const;
          } catch (e) {
            console.error(`error parsing ${attachment.filename}, `, e);
            return { ...attachment, type: "error", error: e } as const;
          }
        }),

        lfi.mapConcur(
          limitConcur(1, async (attachment) => {
            if (attachment.type !== "success") return attachment;

            const fileHash = await attachment.motlFile.getHash();

            const alreadyExists = await prisma.invoiceImportItem.findFirst({
              where: {
                fileHash,
              },
            });

            if (alreadyExists) {
              console.log(`invoice import item already exists for ${attachment.filename}, skipping`);
              return {
                type: "alreadyExists",
                alreadyExists,
                attachment,
              } as const;
            }

            if (!attachment.attachmentId) {
              console.error(`no attachmentId for ${attachment.filename}, skipping`);
              return { ...attachment, type: "error", error: new Error("no attachmentId") } as const;
            }

            await prisma.invoiceImportItem.create({
              data: {
                externalId: attachment.attachmentId!,
                importItemType: "GMAIL_ATTACHMENT",
                text: attachment.parsedText,
                fileUrl: attachment.fileUrl,
                fileHash,
                filename: attachment.filename!,
                itemDate: date.parseUnix(attachment.message.internalDate!),
              },
            });

            return attachment;
          })
        ),

        lfi.reduceConcur(lfi.toArray())
      );

    // parse flage --limit with yargs
    const args = await yargs(process.argv.slice(2))
    .option("limit", { type: "number", default: 2 })
    .option("mail", { type: "string", default: "matt" })
    .parse();

    const email = args.mail + "@stonedapecrew.com";

    const dataPath = `./data-local/${email}`;

    console.log("fetching gmail attachemnts");
    const allMessagesWithAttachments = await fetchGmailAttachemnts({
      gmailClient: google.getGmailClient(email),
      testArgs: { limit: args.limit },
    });

    console.log("fetched gmail attachemnts");

    const success = allMessagesWithAttachments.filter((a) => a.type === "success");
    const failed = allMessagesWithAttachments.filter((a) => a.type !== "success");
    const total = allMessagesWithAttachments.length;

    console.log("first", date.parseUnix(success[0].message.internalDate!));
    console.log("last", date.parseUnix(success[success.length - 1].message.internalDate!));

    const alreadyExists = allMessagesWithAttachments.filter((a) => a.type === "alreadyExists");

    console.log(`found ${alreadyExists.length} already existing attachments`);

    const pdfs = success.filter((a) => a.fileType === "pdf");
    const nonPdfs = success.filter((a) => a.fileType !== "pdf");

    const dataTable = aq.from(success);

    const columns = dataTable.columnNames();
    console.log(columns);

    fs.mkdirSync(dataPath, { recursive: true });
    fs.writeFileSync(`${dataPath}/attachments.arrow`, dataTable.toArrowIPC());

    console.log(`${success.length} of ${total} attachments were successfully parsed`);
    console.log(`found ${pdfs.length} pdfs`);

    if (failed.length > 0) {
      console.error(`failed to parse ${failed.length}/${total} attachments`);
    } else {
      console.log(`successfully parsed ${success.length}/${total} attachments`);
    }

    console.log(`Found ${allMessagesWithAttachments.length} attachments`);

    const uniqueMessageIds = R.uniqueBy(allMessagesWithAttachments, (m) => m.message.id!);
    console.log(`found ${uniqueMessageIds.length} unique messages`);

    console.log(`found ${allMessagesWithAttachments.length} messages with attachments`);

    const averagePagesCount = R.mean(pdfs.map((a) => a.pagesCount!));

    const lowestPagesCount = R.firstBy(pdfs, [(a) => a.pagesCount!, "asc"]);
    const highestPagesCount = R.firstBy(pdfs, [(a) => a.pagesCount!, "desc"]);

    console.log(`average pages count: ${averagePagesCount}`);
    console.log(`lowest pages count: ${lowestPagesCount?.pagesCount ?? "N/A"}`);
    console.log(`highest pages count: ${highestPagesCount?.pagesCount ?? "N/A"}`);

    const emailWithPlainText = R.filter(uniqueMessageIds, (m) => !!m.message.body.bodyPlain);
    const emailWithHtml = R.filter(uniqueMessageIds, (m) => !!m.message.body.bodyHtml);
    const emailWithText = R.filter(uniqueMessageIds, (m) => !!m.message.body.bodyText);

    console.log(`found ${emailWithPlainText.length} messages with plain text`);
    console.log(`found ${emailWithHtml.length} messages with html`);
    console.log(`found ${emailWithText.length} messages with text`);

    const totalAttachmentsSizeMB = R.sum(allMessagesWithAttachments.map((a) => a.size!)) / (1024 * 1024);
    console.log(`Total attachments size: ${totalAttachmentsSizeMB.toFixed(2)} MB`);
  });

  const debug = 1;
}

getAllMail().catch(console.error);
