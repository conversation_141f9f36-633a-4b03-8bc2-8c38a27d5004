/**
 * Reconciliation CLI
 *
 * This CLI provides tools for finding and managing reconciliations between invoices and transfers.
 *
 * Usage Examples:
 *
 * # Show reconciliation statistics
 * pnpm tsx src/modules/reconciliation/reconciliationCli.ts stats
 *
 * # Find potential matches (preview only)
 * pnpm tsx src/modules/reconciliation/reconciliationCli.ts find-matches --limit 10
 *
 * # Find and store reconciliations with custom confidence threshold
 * pnpm tsx src/modules/reconciliation/reconciliationCli.ts find-matches --store --min-confidence 70
 *
 * # Find matches for a specific invoice
 * pnpm tsx src/modules/reconciliation/reconciliationCli.ts find-for-invoice <invoice-id> --limit 5
 *
 * # Find and store reconciliation for a specific invoice
 * pnpm tsx src/modules/reconciliation/reconciliationCli.ts find-for-invoice <invoice-id> --store --min-confidence 50
 *
 * # Auto-reconcile all unmatched invoices (preview)
 * pnpm tsx src/modules/reconciliation/reconciliationCli.ts find-for-all-invoices
 *
 * # Auto-reconcile all unmatched invoices (store)
 * pnpm tsx src/modules/reconciliation/reconciliationCli.ts find-for-all-invoices --store
 *
 * # Find invoice matches for transfers from specific account
 * pnpm tsx src/modules/reconciliation/reconciliationCli.ts find-from-transfers --account-name "Wise"
 *
 * # Find invoice matches for transfers with specific counterparty and currency
 * pnpm tsx src/modules/reconciliation/reconciliationCli.ts find-from-transfers --counterparty "Upwork" --currency-code "USD"
 *
 * # Find invoice matches for transfers within amount range
 * pnpm tsx src/modules/reconciliation/reconciliationCli.ts find-from-transfers --min-amount 100 --max-amount 5000
 *
 * # Output in JSON format
 * pnpm tsx src/modules/reconciliation/reconciliationCli.ts find-matches --format json --limit 5
 */

import yargs from "yargs";
import { hideBin } from "yargs/helpers";
import {
  findPotentialMatches,
  findPotentialMatchesFromTransfers,
  autoReconcile,
  autoReconcileForInvoice,
  getReconciliationStats,
  ReconciliationConfig,
  DEFAULT_CONFIG,
} from "./reconciliationService";
import { json } from "../core/utils/jsonUtils";
import * as R from "remeda";
import { prisma } from "@/prisma/prisma";
import { Prisma } from "@/prisma/generated";

interface CliOptions {
  store?: boolean;
  minConfidence?: number;
  maxDays?: number;
  amountTolerance?: number;
  limit?: number;
  format?: "table" | "json";
  invoiceId?: string;
  accountName?: string;
  counterparty?: string;
  currencyCode?: string;
  minAmount?: number;
  maxAmount?: number;
}

/**
 * Find potential matches for all unmatched invoices and transfers
 */
async function findMatches(args: CliOptions) {
  console.log("🔍 Finding potential reconciliation matches...");

  const config: ReconciliationConfig = {
    maxDaysDifference: args.maxDays ?? DEFAULT_CONFIG.maxDaysDifference,
    minAutoMatchConfidence: args.minConfidence ?? DEFAULT_CONFIG.minAutoMatchConfidence,
    amountToleranceFraction: args.amountTolerance ?? DEFAULT_CONFIG.amountToleranceFraction,
  };

  const matchesResult = await findPotentialMatches({
    ...config,
  });

  if (matchesResult.isErr()) {
    console.error("❌ Failed to find matches:", matchesResult.error.message);
    return;
  }

  const matches = matchesResult.value;
  const limitedMatches = args.limit ? matches.slice(0, args.limit) : matches;

  console.log(`\n📊 Found ${matches.length} total matches`);

  const autoMatches = matchesResult.value.filter((match) => match.confidenceScore >= config.minAutoMatchConfidence);

  console.log(`📋 Showing ${autoMatches.length} auto-matches`);
  const uniqueInvoiceAutoMatches = R.pipe(
    autoMatches,
    R.uniqueBy((match) => match.invoiceId),
    R.uniqueBy((match) => match.transferId)
  );
  console.log(`📋 Showing ${uniqueInvoiceAutoMatches.length} unique invoice auto-matches`);

  if (args.limit && matches.length > args.limit) {
    console.log(`📋 Showing top ${args.limit} matches (use --limit to adjust)`);
  }

  if (args.format === "json") {
    console.log(json.prettyPrint(limitedMatches));
  } else {
    displayMatchesTable(limitedMatches);
  }

  if (args.store) {
    console.log("\n💾 Storing reconciliations...");
    const autoMatchResult = await autoReconcile(config);

    if (autoMatchResult.isErr()) {
      console.error("❌ Failed to store reconciliations:", autoMatchResult.error.message);
      return;
    }

    console.log(`✅ Stored ${autoMatchResult.value} automatic reconciliations`);
  } else {
    console.log("\n💡 Use --store to save these reconciliations to the database");
  }
}

/**
 * Find matches for a specific invoice
 */
async function findForInvoice(args: CliOptions) {
  if (!args.invoiceId) {
    console.error("❌ Invoice ID is required");
    return;
  }

  console.log(`🔍 Finding matches for invoice ${args.invoiceId}...`);

  // Get invoice details first
  const invoice = await prisma.invoice.findUnique({
    where: { id: args.invoiceId },
    include: {
      vendor: { select: { name: true } },
      reconciliations: true,
    },
  });

  if (!invoice) {
    console.error("❌ Invoice not found");
    return;
  }

  console.log(`📄 Invoice: ${invoice.invoiceReference} - ${invoice.vendor.name}`);
  console.log(`💰 Amount: ${invoice.currencyCode} ${invoice.amountGross}`);
  console.log(`📅 Date: ${invoice.date.toISOString().split("T")[0]}`);

  if (invoice.reconciliations.length > 0) {
    console.log(`⚠️  Invoice already has ${invoice.reconciliations.length} reconciliation(s)`);
  }

  const config: ReconciliationConfig = {
    minAutoMatchConfidence: args.minConfidence ?? DEFAULT_CONFIG.minAutoMatchConfidence,
    maxDaysDifference: args.maxDays ?? DEFAULT_CONFIG.maxDaysDifference,
    amountToleranceFraction: args.amountTolerance ?? DEFAULT_CONFIG.amountToleranceFraction,
  };

  // Find all potential matches for this invoice
  const allMatchesResult = await findPotentialMatches({ ...config, invoiceId: args.invoiceId });

  if (allMatchesResult.isErr()) {
    console.error("❌ Failed to find matches:", allMatchesResult.error.message);
    return;
  }

  const invoiceMatches = allMatchesResult.value.filter((match) => match.invoiceId === args.invoiceId);
  const limitedMatches = args.limit ? invoiceMatches.slice(0, args.limit) : invoiceMatches;

  console.log(`\n📊 Found ${invoiceMatches.length} potential matches for this invoice`);

  if (args.format === "json") {
    console.log(json.prettyPrint(limitedMatches));
  } else {
    displayMatchesTable(limitedMatches);
  }

  if (args.store && invoiceMatches.length > 0) {
    console.log("\n💾 Storing reconciliation for this invoice...");
    const autoMatchResult = await autoReconcileForInvoice(args.invoiceId, config);

    if (autoMatchResult.isErr()) {
      console.error("❌ Failed to store reconciliation:", autoMatchResult.error.message);
      return;
    }

    console.log(`✅ Stored ${autoMatchResult.value} reconciliation(s) for this invoice`);
  } else if (args.store) {
    console.log("\n💡 No matches found to store");
  } else {
    console.log("\n💡 Use --store to save the best match to the database");
  }
}

/**
 * Find matches for all unmatched invoices
 */
async function findForAllInvoices(args: CliOptions) {
  console.log("🔍 Finding matches for all unmatched invoices...");

  // Get all unmatched invoices
  const unmatchedInvoices = await prisma.invoice.findMany({
    where: {
      reconciliations: {
        none: {},
      },
      vendor: {
        name: {
          search: "upwork",
        },
      },
    },
    include: {
      vendor: {
        select: {
          name: true,
        },
      },
    },
  });

  console.log(`📋 Found ${unmatchedInvoices.length} unmatched invoices`);

  if (unmatchedInvoices.length === 0) {
    console.log("✅ All invoices are already matched!");
    return;
  }

  const config: ReconciliationConfig = {
    maxDaysDifference: args.maxDays ?? DEFAULT_CONFIG.maxDaysDifference,
    minAutoMatchConfidence: args.minConfidence ?? DEFAULT_CONFIG.minAutoMatchConfidence,
    amountToleranceFraction: args.amountTolerance ?? DEFAULT_CONFIG.amountToleranceFraction,
  };

  if (args.store) {
    console.log("\n💾 Auto-reconciling all unmatched invoices...");
    const autoMatchResult = await autoReconcile(config);

    if (autoMatchResult.isErr()) {
      console.error("❌ Failed to auto-reconcile:", autoMatchResult.error.message);
      return;
    }

    console.log(`✅ Stored ${autoMatchResult.value} automatic reconciliations`);
  } else {
    // Just show a summary of what would be matched
    const matchesResult = await findPotentialMatches(config);

    if (matchesResult.isErr()) {
      console.error("❌ Failed to find matches:", matchesResult.error.message);
      return;
    }

    const matches = matchesResult.value;
    const autoMatches = matches.filter((match) => match.confidenceScore >= config.minAutoMatchConfidence);

    console.log(`\n📊 Found ${autoMatches.length} potential auto-matches`);
    console.log("\n💡 Use --store to save these reconciliations to the database");
  }
}

/**
 * Find invoice matches for filtered transfers
 */
async function findFromTransfers(args: CliOptions) {
  console.log("🔍 Finding invoice matches for filtered transfers...");

  // Build transfer where clause based on CLI arguments
  const transferWhereClause: Prisma.TransferWhereInput = {};

  if (args.accountName) {
    transferWhereClause.transaction = {
      account: {
        name: {
          contains: args.accountName,
          mode: "insensitive",
        },
      },
    };
  }

  if (args.counterparty) {
    transferWhereClause.counterparty = {
      contains: args.counterparty,
      mode: "insensitive",
    };
  }

  if (args.currencyCode) {
    transferWhereClause.currencyCode = args.currencyCode;
  }

  if (args.minAmount !== undefined || args.maxAmount !== undefined) {
    transferWhereClause.amount = {};
    if (args.minAmount !== undefined) {
      transferWhereClause.amount.gte = args.minAmount;
    }
    if (args.maxAmount !== undefined) {
      transferWhereClause.amount.lte = args.maxAmount;
    }
  }

  console.log("🔧 Transfer filters:", JSON.stringify(transferWhereClause, null, 2));

  const config: ReconciliationConfig = {
    maxDaysDifference: args.maxDays ?? DEFAULT_CONFIG.maxDaysDifference,
    minAutoMatchConfidence: args.minConfidence ?? DEFAULT_CONFIG.minAutoMatchConfidence,
    amountToleranceFraction: args.amountTolerance ?? DEFAULT_CONFIG.amountToleranceFraction,
  };

  const matchesResult = await findPotentialMatchesFromTransfers(transferWhereClause, config);

  if (matchesResult.isErr()) {
    console.error("❌ Failed to find matches:", matchesResult.error.message);
    return;
  }

  const matches = matchesResult.value;
  const limitedMatches = args.limit ? matches.slice(0, args.limit) : matches;

  console.log(`\n📊 Found ${matches.length} total matches`);

  const autoMatches = matches.filter((match) => match.confidenceScore >= config.minAutoMatchConfidence);
  console.log(`📋 Found ${autoMatches.length} auto-matches`);

  if (args.limit && matches.length > args.limit) {
    console.log(`📋 Showing top ${args.limit} matches (use --limit to adjust)`);
  }

  if (args.format === "json") {
    console.log(json.prettyPrint(limitedMatches));
  } else {
    displayMatchesTable(limitedMatches);
  }

  if (args.store && autoMatches.length > 0) {
    console.log("\n💾 Storing reconciliations...");
    // Note: We would need to implement a specific auto-reconcile function for filtered transfers
    // For now, we'll just show what would be stored
    console.log(`💡 Would store ${autoMatches.length} automatic reconciliations`);
    console.log("💡 Auto-store for filtered transfers not yet implemented - use find-matches --store instead");
  } else if (args.store) {
    console.log("\n💡 No auto-matches found to store");
  } else {
    console.log("\n💡 Use --store to save these reconciliations to the database");
  }
}

/**
 * Display reconciliation statistics
 */
async function showStats() {
  console.log("📊 Fetching reconciliation statistics...");

  const statsResult = await getReconciliationStats();

  if (statsResult.isErr()) {
    console.error("❌ Failed to get statistics:", statsResult.error.message);
    return;
  }

  const stats = statsResult.value;

  console.log("\n=== Reconciliation Statistics ===");
  console.log(`Total Invoices: ${stats.totalInvoices}`);
  console.log(`Total Transfers: ${stats.totalTransfers}`);
  console.log(`Matched Invoices: ${stats.matchedInvoices} (${Math.round((stats.matchedInvoices / stats.totalInvoices) * 100)}%)`);
  console.log(`Matched Transfers: ${stats.matchedTransfers} (${Math.round((stats.matchedTransfers / stats.totalTransfers) * 100)}%)`);
  console.log(`Auto-Matched: ${stats.autoMatched}`);
  console.log(`Manually Matched: ${stats.manuallyMatched}`);
  console.log(`Potential Matches: ${stats.potentialMatches}`);
}

/**
 * Display matches in a table format
 */
function displayMatchesTable(matches: any[]) {
  if (matches.length === 0) {
    console.log("No matches found");
    return;
  }

  console.log("\n=== Potential Matches ===");
  console.log("Score | Invoice Date | Invoice Ref | Vendor | Amount | Transfer Date | Transfer Amount | Reason | Invoice ID | Tx ID");
  console.log("------|-------------|------------|--------|--------|--------------|----------------|-------|------------|------------");

  matches.forEach((match) => {
    const invoice = match.invoice;
    const transfer = match.transfer;

    console.log(
      `${match.confidenceScore.toString().padEnd(5)} | ` +
        `${invoice.date.toISOString().split("T")[0]} | ` +
        `${invoice.invoiceReference.substring(0, 10).padEnd(10)} | ` +
        `${invoice.vendor.name.substring(0, 15).padEnd(15)} | ` +
        `${invoice.currencyCode} ${Number(invoice.amountGross).toFixed(2).padEnd(8)} | ` +
        `${transfer.transaction.executedAt.toISOString().split("T")[0]} | ` +
        `${transfer.currencyCode} ${Number(transfer.amount).toFixed(2).padEnd(8)} | ` +
        `${match.matchReason} | ` +
        `${invoice.id} | ` +
        `${transfer.transactionId}`
    );
  });
}

/**
 * Main CLI function
 */
async function main() {
  try {
    await yargs(hideBin(process.argv))
      .command(
        "find-matches",
        "Find potential matches between unmatched invoices and transfers",
        (yargs) => yargs,
        (argv) => findMatches(argv as CliOptions)
      )
      .command(
        "for-invoice <invoiceId>",
        "Find potential matches for a specific invoice",
        (yargs) =>
          yargs.positional("invoiceId", {
            describe: "Invoice ID to find matches for",
            type: "string",
          }),
        (argv) => findForInvoice(argv as CliOptions)
      )
      .command(
        "ffor-all-invoices",
        "Find matches for all unmatched invoices",
        (yargs) => yargs,
        (argv) => findForAllInvoices(argv as CliOptions)
      )
      .command(
        "from-transfers",
        "Find invoice matches for filtered transfers",
        (yargs) => yargs,
        (argv) => findFromTransfers(argv as CliOptions)
      )
      .command(
        "stats",
        "Show reconciliation statistics",
        (yargs) => yargs,
        () => showStats()
      )
      .option("store", {
        type: "boolean",
        default: false,
        description: "Store reconciliations in the database",
      })
      .option("min-confidence", {
        type: "number",
        description: "Minimum confidence score for auto-matching (0-100)",
      })
      .option("max-days", {
        type: "number",
        description: "Maximum days difference between invoice and transfer dates",
      })
      .option("amount-tolerance", {
        type: "number",
        description: "Amount tolerance as a fraction (e.g., 0.02 for 2%)",
      })
      .option("limit", {
        type: "number",
        description: "Maximum number of matches to display",
        default: 10,
      })
      .option("format", {
        type: "string",
        choices: ["table", "json"],
        default: "table",
        description: "Output format",
      })
      .option("account-name", {
        type: "string",
        description: "Filter transfers by account name (partial match)",
      })
      .option("counterparty", {
        type: "string",
        description: "Filter transfers by counterparty (partial match)",
      })
      .option("currency-code", {
        type: "string",
        description: "Filter transfers by currency code (exact match)",
      })
      .option("min-amount", {
        type: "number",
        description: "Filter transfers by minimum amount",
      })
      .option("max-amount", {
        type: "number",
        description: "Filter transfers by maximum amount",
      })
      .option("vendor-name", {
        type: "string",
        description: "Filter invoices by vendor name (partial match)",
      })
      .demandCommand(1, "Please specify a command")
      .help()
      .parse();
  } catch (error) {
    console.error("❌ CLI error:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the CLI if this file is executed directly
if (require.main === module) {
  main();
}
